import { FormControl, FormGroup, Validators } from '@angular/forms';
import { SearchModelV2 } from 'projects/shared-utilities/models/search-model-v2';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';

export const SalesReportByDailyWeeklyMonthlySearchModel: SearchModelV2 = {
  label: {
    branch: 'Branch',
    date: 'Date',
    customer: 'Customer',
    salesman: 'Salesman',
    itemType: 'Item Type',
    itemStatus: 'Item Status',
    itemCategoryLevel1: "Item Category Level 1",
    itemCategoryLevel2: "Item Category Level 2",
    itemCategoryLevel3: "Item Category Level 3",
    itemCategoryLevel4: "Item Category Level 4",
    itemCategoryLevel5: "Item Category Level 5",
    itemCategoryLevel6: "Item Category Level 6",
    itemCategoryLevel7: "Item Category Level 7",
    itemCategoryLevel8: "Item Category Level 8",
    itemCategoryLevel9: "Item Category Level 9",
    itemCategoryLevel10: "Item Category Level 10",
  },
  dataType: {
    branch: 'select-multi-branch',
    date: 'date',
    dateUpdate: 'date',
    customer: 'select-multi-entity',
    salesman: 'select-multi-entity',
    itemType: ['select', ['BASIC_ITEM','GROUPED_ITEM', 'NSTI', 'BUNDLE','COUPON','SERVICE','WARRANTY','GL_CODE','DOC_HEADER_ADJUSTMENT','MEMBERSHIP','MADE_TO_ORDER','DIGITAL_GOODS']],
    itemStatus: ['select', ['ACTIVE','INACTIVE','DELETED']],
    itemCategoryLevel1: 'select-multi-item-category-level',
    itemCategoryLevel2: 'select-multi-item-category-level',
    itemCategoryLevel3: 'select-multi-item-category-level',
    itemCategoryLevel4: 'select-multi-item-category-level',
    itemCategoryLevel5: 'select-multi-item-category-level',
    itemCategoryLevel6: 'select-multi-item-category-level',
    itemCategoryLevel7: 'select-multi-item-category-level',
    itemCategoryLevel8: 'select-multi-item-category-level',
    itemCategoryLevel9: 'select-multi-item-category-level',
    itemCategoryLevel10: 'select-multi-item-category-level',
  },
  options: {
    branch: {'multiple': true},
    date: {'required': true},
    customer: {'multiple': true, 'entity': 'customer'},
    salesman: {'multiple': true, 'entity': 'employee'},
    itemCategory: {'multiple': true},
    itemType: {'multiple': true},
    itemStatus: {'required': true},
    itemCategoryLevel1: {'level': 1, 'multiple': true},
    itemCategoryLevel2: {'level': 2, 'multiple': true},
    itemCategoryLevel3: {'level': 3, 'multiple': true},
    itemCategoryLevel4: {'level': 4, 'multiple': true},
    itemCategoryLevel5: {'level': 5, 'multiple': true},
    itemCategoryLevel6: {'level': 6, 'multiple': true},
    itemCategoryLevel7: {'level': 7, 'multiple': true},
    itemCategoryLevel8: {'level': 8, 'multiple': true},
    itemCategoryLevel9: {'level': 9, 'multiple': true},
    itemCategoryLevel10: {'level': 10, 'multiple': true},
  },

  form: new FormGroup({
    branch: new FormControl(),
    date: new FormGroup({
      from: new FormControl(UtilitiesModule.getTodayNoTime()),
      to: new FormControl(UtilitiesModule.getTodayNoTime())
    }),
    customer: new FormControl(),
    salesman: new FormControl(),
    itemCategory: new FormControl(),
    itemType: new FormControl(),
    itemStatus: new FormControl('ACTIVE'),
    itemCategoryLevel1: new FormControl(),
    itemCategoryLevel2: new FormControl(),
    itemCategoryLevel3: new FormControl(),
    itemCategoryLevel4: new FormControl(),
    itemCategoryLevel5: new FormControl(),
    itemCategoryLevel6: new FormControl(),
    itemCategoryLevel7: new FormControl(),
    itemCategoryLevel8: new FormControl(),
    itemCategoryLevel9: new FormControl(),
    itemCategoryLevel10: new FormControl(),
  })
};
