// Angular Core
import { ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

// NgRx
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';

// Third-Party Libraries
import { GridOptions } from 'ag-grid-enterprise';
import * as moment from 'moment';
import { ErrorMessages } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { Observable, from } from 'rxjs';
import { filter, map, mergeMap, take, toArray } from 'rxjs/operators';
import { SubSink } from 'subsink2';

// Shared Utilities - Models
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';

// Shared Utilities - Permissions
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';

// Shared Utilities - Session
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';

// Shared Utilities - Components & Utilities
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';

// Application Imports
import { ApiService } from '../../../services/api-service';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { MultiBranchSalesPurchaseCollectionSearchModel } from '../../../models/advanced-search-models/multi-branch-sales-purchase-collection-search.model';
import { MultiBranchDailySalesPurchaseInputDto, SalesReportByItemCodeModel } from '../../../models/sales-report-by-item-code-model';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-multi-branch-sales-purchase-collection',
  templateUrl: './multi-branch-sales-purchase-collection.component.html',
  styleUrls: ['./multi-branch-sales-purchase-collection.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class MultiBranchSalesPurchaseCollectionComponent extends ViewColumnComponent implements OnInit, OnDestroy {

  protected subs = new SubSink();
  
  compId = 'multiBranchSalesPurchaseCollection'
  compName = 'Multi Branch Sales Purchase Collection';
  protected readonly index = 0;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  protected localState: LocalState;

  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);
  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  toggleColumn$: Observable<boolean>;
  searchModel = MultiBranchSalesPurchaseCollectionSearchModel;

  showColumns = [
    { name: 'sales_cost', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
    { name: 'landed_ma_amount', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp_landed', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage_landed', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
  ]

  rowData = [];
  totalRecords = 0;
  salesCost = 'cost_ma';
  branchGuids: string[] = [];
  subTitle = '';

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;

  gridOptions: GridOptions = {
    pagination: false,
    groupIncludeTotalFooter: true,
  };
  
  columnsDefs;

  readPermissionDefintion = {
		branch : 'API_TNT_DM_ERP_MULTI_BRANCH_DAILY_SALES_PURCHASE_COLLECTION_READ'
	}

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected apiService: ApiService,
    protected toastr: ToastrService,
    protected readonly componentStore: ComponentStore<LocalState>,
    private readonly permissionStore: Store<PermissionStates>,
    protected readonly sessionStore: Store<SessionStates>) {
    super();
  }

  ngOnInit() {

    this.branchGuids = UtilitiesModule.getTargetsByPermission(this.userPermissionTarget$,
      this.readPermissionDefintion.branch, 'bl_fi_mst_branch'
    );
    
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.viewColFacade.rowDataByMBS$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.viewColFacade.totalRecords$.subscribe(totalRecords => this.totalRecords = totalRecords);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.columnsDefs = [
      {
        headerName: 'Date',
        field: 'date',
        type: 'dateColumn',
        rowGroup: true
      },
      {
        headerName: 'Day',
        field: 'day',
        type: 'textColumn',
        rowGroup: true
      },
      {
        headerName: 'branch',
        field: 'branch_code',
        type: 'textColumn',
        pivot: true,
        pivotComparator: (a: string, b: string) => {
          if (a === 'Total') return 1;
          if (b === 'Total') return -1;
          return a.localeCompare(b);
        },
      },
      {
        headerName: 'Sales Before Tax',
        field: 'total_sales_amount_net',
        type: 'decimalColumn'
      },
      {
        headerName: 'Sales Tax',
        field: 'total_sales_amount_tax',
        type: 'decimalColumn'
      },
      {
        headerName: 'GP',
        field: 'gp',
        type: 'decimalColumn'
      },
      {
        headerName: 'GP %',
        field: 'gp_percentage',
        type: 'decimalColumn'
      },
      {
        headerName: 'Sales After Tax',
        field: 'total_sales_amount_txn',
        type: 'decimalColumn'
      },
      {
        headerName: 'GRN Before Tax',
        field: 'total_grn_amount_net',
        type: 'decimalColumn'
      },
      {
        headerName: 'GRN Tax',
        field: 'total_grn_amount_tax',
        type: 'decimalColumn'
      },
      {
        headerName: 'GRN After Tax',
        field: 'total_grn_amount_txn',
        type: 'decimalColumn'
      },
      {
        headerName: 'Trade In',
        field: 'total_trade_in_amount_txn',
        type: 'decimalColumn'
      },
      {
        headerName: 'Collection',
        field: 'total_rct_amount_txn',
        type: 'decimalColumn'
      },
      {
        headerName: 'Payment',
        field: 'total_pv_amount_txn',
        type: 'decimalColumn'
      }
    ];
  }

  onNext() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false
    });
    this.viewColFacade.onNextAndReset(this.index, 1);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
    else
    {
      //this.createData();
    }
    const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
    statusBarComponent.setTitle(this.compName);
    statusBarComponent.setFilename(this.compName);
    statusBarComponent.setHideGroupColumn(true);
  }

  getColDefMonth(){
    this.columnsDefs = [
      {
        headerName: 'Date',
        field: 'date',
        type: 'textColumn'
      },
      {
        headerName: 'branch',
        field: 'branch_code',
        type: 'textColumn'
      },
      {
        headerName: 'Sales Before Tax',
        field: 'total_sales_amount_net',
        type: 'decimalColumn'
      },
      {
        headerName: 'Sales Tax',
        field: 'total_sales_amount_tax',
        type: 'decimalColumn'
      },
      {
        headerName: 'Sales After Tax',
        field: 'total_sales_amount_txn',
        type: 'decimalColumn'
      },
      {
        headerName: 'GP',
        field: 'gp',
        type: 'decimalColumn'
      },
      {
        headerName: 'GP %',
        field: 'gp_percentage',
        type: 'decimalColumn'
      },
      {
        headerName: 'GRN Before Tax',
        field: 'total_grn_amount_net',
        type: 'decimalColumn'
      },
      {
        headerName: 'GRN Tax',
        field: 'total_grn_amount_tax',
        type: 'decimalColumn'
      },
      {
        headerName: 'GRN After Tax',
        field: 'total_grn_amount_txn',
        type: 'decimalColumn'
      },
      {
        headerName: 'Trade In',
        field: 'total_trade_in_amount_txn',
        type: 'decimalColumn'
      },
      {
        headerName: 'Collection',
        field: 'total_rct_amount_txn',
        type: 'decimalColumn'
      },
      {
        headerName: 'Payment',
        field: 'total_pv_amount_txn',
        type: 'decimalColumn'
      }
    ];
  }

  createData(inputModel?: MultiBranchDailySalesPurchaseInputDto) {
    if(inputModel.date_option==="MONTH" || inputModel.date_option==="QUARTER"){
      this.getColDefMonth();
    }
    this.clear();
    this.viewColFacade.loadInit(this.gridApi);
    this.subs.sink = this.apiService.getMultiBranchSalesPurchaseCollection(inputModel, this.apiVisa).pipe(
      mergeMap(a => from(a.data).pipe(
        filter(b => b.branch_code !== null),
        map(b => {
          Object.assign(b,
            {
              qty: Math.abs(b.qty_sold),
              cost: b.total_cost_ma,
              gp:b.total_sales_amount_net - (b.total_cost_ma),
              gp_percentage: ((b.total_sales_amount_net - (b.total_cost_ma))/(b.total_sales_amount_net))*100,
              net: b.total_sales_amount_txn - b.total_return_amount_txn,
              avg: (b.total_sales!==0?b.total_sales_amount_txn / b.total_sales:b.total_sales_amount_txn)
            }
          )
          return b;
        }),
        toArray(),
        map(c => {
          a.data = c;
          return a;
        })
      ))
    ).subscribe(resolved => {
      console.log('Data before grouping:', resolved);

      // Group data by date
      const groupedData = resolved.data.reduce((acc, entry) => {
        const date = entry.date;
        if (!acc[date]) acc[date] = [];
        acc[date].push(entry);
        return acc;
      }, {});

      // Final data array with totals for each date
      const finalData = [];
      Object.keys(groupedData).forEach(date => {
        const entries = groupedData[date];

        // Calculate totals for each field for this date
        const dateTotal = entries.reduce((totals, entry) => {
          return {
            date,
            branch_code: 'Total',
            branch_name: 'Total',
            day: entry.day,
            total_sales_amount_net: (totals.total_sales_amount_net || 0) + (entry.total_sales_amount_net || 0),
            total_sales_amount_tax: (totals.total_sales_amount_tax || 0) + (entry.total_sales_amount_tax || 0),
            total_return_amount_tax: (totals.total_return_amount_tax || 0) + (entry.total_return_amount_tax || 0),
            total_sales_amount_txn: (totals.total_sales_amount_txn || 0) + (entry.total_sales_amount_txn || 0),
            total_cost_ma: (totals.total_cost_ma || 0) + (entry.total_cost_ma || 0),
            total_return_amount_txn: (totals.total_return_amount_txn || 0) + (entry.total_return_amount_txn || 0),
            total_grn_amount_net: (totals.total_grn_amount_net || 0) + (entry.total_grn_amount_net || 0),
            total_grn_amount_txn: (totals.total_grn_amount_txn || 0) + (entry.total_grn_amount_txn || 0),
            total_grn_amount_tax: (totals.total_grn_amount_tax || 0) + (entry.total_grn_amount_tax || 0),
            total_trade_in_amount_txn: (totals.total_trade_in_amount_txn || 0) + (entry.total_trade_in_amount_txn || 0),
            total_rct_amount_txn: (totals.total_rct_amount_txn || 0) + (entry.total_rct_amount_txn || 0),
            total_pv_amount_txn: (totals.total_pv_amount_txn || 0) + (entry.total_pv_amount_txn || 0),
            total_return_amount_net: (totals.total_return_amount_net || 0) + (entry.total_return_amount_net || 0),
            qty: null,
            cost: (totals.cost || 0) + (entry.cost || 0),
            gp: (totals.gp || 0) + (entry.gp || 0),
            net: (totals.net || 0) + (entry.net || 0),
            avg: null
          };
        }, {});

        // Calculate gp_percentage for the total
        dateTotal.gp_percentage = dateTotal.total_sales_amount_net
          ? (dateTotal.gp / dateTotal.total_sales_amount_net) * 100
          : null;

        // Add original entries and the calculated total row for this date to finalData
        finalData.push(...entries);
      });

      finalData.sort((a, b) => b.total_rct_amount_txn - a.total_rct_amount_txn);
      console.log('Final data with totals:', finalData);
      this.viewColFacade.loadSuccess({ data: finalData });
      this.totalRecords = finalData.length;
      this.rowData = finalData;
      this.gridApi.setRowData(this.rowData);
      this.viewColFacade.selectRowDataByMBS(this.rowData);
      this.viewColFacade.selectTotalRecords(this.totalRecords);
    }, err => {
      console.error(err);
      let msg = err.status===403 ? ErrorMessages.CRUD_403_ERRORS.READ : err.message;
      this.toastr.error(
        msg,
        "AG Gird Error",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      this.viewColFacade.loadFailed(err);
    });
  };

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onSearch(e: SearchQueryModel) {
    console.log("search", e);
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3)  {
        this.toastr.error(
          'Search keyword must more than 2 characters.',
          'Keyword',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      }

      const inputModel = {} as MultiBranchDailySalesPurchaseInputDto;
      inputModel.keyword = e.keyword;
      inputModel.date_from = '2022-01-01T00:00:00.000Z';
      inputModel.date_to = '2099-12-31T00:00:00.000Z';
      if (e.queryString) {
        inputModel.keyword = UtilitiesModule.checkNull(e.queryString['itemCode'],'');
        inputModel.branch_guids = UtilitiesModule.checkNull(e.queryString['branch'],[]);
        inputModel.date_from = UtilitiesModule.checkNull(e.queryString['date']['from'],'2022-01-01T00:00:00.000Z');
        inputModel.date_to = UtilitiesModule.checkNull(e.queryString['date']['to'],'2099-12-31T00:00:00.000Z');
        inputModel.date_option = UtilitiesModule.checkNull(e.queryString['dateOption'],'');

        if (inputModel.date_option === 'MONTH') {
          const fromDate = e.queryString['date']['from'];
          const toDate = e.queryString['date']['to'];

          const startOfMonth = moment(fromDate).startOf('month').add(1, 'day').format('YYYY-MM-DDT00:00:00.000Z');
          const endOfMonth = moment(toDate).endOf('month').format('YYYY-MM-DDT23:59:59.999Z');

          inputModel.date_from = UtilitiesModule.checkNull(startOfMonth, '2022-01-01T00:00:00.000Z');
          inputModel.date_to = UtilitiesModule.checkNull(endOfMonth, '2099-12-31T00:00:00.000Z');
        }
        inputModel.customer_category_guids = UtilitiesModule.checkNull(e.queryString['customerCategory'],[]);
        inputModel.item_type = UtilitiesModule.checkNull(e.queryString['itemType'],[]);
      }

      // Filter by branch permissions
      inputModel.branch_guids = this.branchGuids;

      console.log('inputModel', inputModel);
      this.createData(inputModel);

      const date_from = UtilitiesModule.dateFormatter(inputModel.date_from);
      const date_to = UtilitiesModule.dateFormatter(inputModel.date_to);
      
      // Set branch names asynchronously
	    this.setBranchSubTitle(inputModel.branch_guids, date_from, date_to);
    }
    else {
      //this.createData();
    }
  }

  clear() {
    this.gridApi.setRowData([]);
    this.totalRecords = 0;
    this.rowData = [];
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  setDataRowCache() {
    console.log('set data row cache');
    this.gridApi.setRowData(this.rowData);
  }

  getSalesCost(model: SalesReportByItemCodeModel) {
    return model[this.salesCost + '_amount'];
  }

  calculateTotal(node, key) {
    if (node.group) {
      let totalCost = 0;
      const children = node.childrenAfterGroup;

      children.forEach((childNode) => {
        const childCost = this.calculateTotal(childNode, key);
        totalCost += childCost;
      });

      return totalCost;
    } else {
      const rowData = node.data;
      return rowData && rowData[key] ? rowData[key] : 0;
    }
  }

  setBranchSubTitle(guids: string[], dateFrom: string, dateTo: string) {
		const key = 'branch';

		this.sessionStore.select(SessionSelectors.selectDropDownRowData).pipe(take(1)).subscribe(resolved => {
		  let rowData = resolved ? resolved[key] ? resolved[key] : [] : [];
		  let branchText = "ALL BRANCHES";

		  if (rowData.length > 0 && guids && guids.length > 0) {
			const branches = rowData.filter(row => guids.includes(row.guid));
			if (branches.length > 0) {
			  branchText = branches.map(branch => `${branch.name} (${branch.code})`).join(', ');
			}
		  }

		  let subTitle = branchText;
		  subTitle += "\n";
		  subTitle += `${dateFrom} to ${dateTo}`;

		  const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
		  statusBarComponent.setSubTitle(subTitle);
		});
  }
}
