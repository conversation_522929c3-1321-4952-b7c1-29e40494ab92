import { Component, ChangeDetectionStrategy, Input, Output, EventEmitter, ViewChild, AfterViewInit, AfterViewChecked, Renderer2, ElementRef } from '@angular/core';
import { Store } from '@ngrx/store';
import { ComponentStore } from '@ngrx/component-store';
import {
  ApiResponseModel,
  StockTakeSessionRecordService,
  Pagination,
  SubQueryService,
  ApiVisa,
} from 'blg-akaun-ts-lib';
import { Observable, of, zip } from 'rxjs';
import { debounceTime, filter, map, switchMap, take, tap } from 'rxjs/operators';
import { ViewColumnFacade } from '../../../../../../facades/view-column.facade';
import { itemRecordSearchModel } from '../../../../../../models/advanced-search-models/item-record.model';
import { AppConfig } from '../../../../../../models/visa';
import { ViewColumnComponent } from '../../../../../view-column.component';
import { StockTakeStates } from '../../../../../../state-controllers/stock-take-controller/store/states';
import { SessionSelectors, DeviceRecordSelectors, SessionDeviceSelectors, ItemSelectors } from '../../../../../../state-controllers/stock-take-controller/store/selectors';
import { DeviceActions, DeviceRecordActions, ItemActions } from '../../../../../../state-controllers/stock-take-controller/store/actions';
import { DeviceRecord as SessionRecordModel, StockTakeItemRecordList } from '../../../../../../models/stock-take.model';
import { SearchQueryModel } from '../../../../../../models/advanced-search-models/query.model';
import { SubSink } from 'subsink2';
import * as moment from 'moment';
import { AkaunMessageDialogComponent } from '../../../../../../../../../../shared-utilities/dialogues/akaun-message-dialog/akaun-message-dialog';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { PaginationComponent } from '../../../../../utilities/pagination/pagination.component';
import { FormControl, FormGroup } from '@angular/forms';
import * as $ from 'jquery';
import Quagga from 'quagga';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors as SessionSelectorShared } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { UUID } from 'angular2-uuid';
import { PaginationClientSideComponent } from 'projects/shared-utilities/utilities/pagination-client-side/pagination-client-side.component';
import { ToastrService } from 'ngx-toastr';
import { AdvancedSearchComponentScan } from '../../../../../utilities/advanced-search-scan/advanced-search.component';

interface LocalState {
  quaggaStatus: 'idle' | 'initializing' | 'ready';
}

@Component({
  selector: 'app-device-record-listing-scan',
  templateUrl: './listing-scan.component.html',
  styleUrls: ['./listing-scan.component.scss'],
  changeDetection: ChangeDetectionStrategy.Default,
  providers: [ComponentStore]
})

export class ListingScanComponent  extends ViewColumnComponent   {
  model: StockTakeItemRecordList;
  @Input() autoScan: boolean;
  @Input() validateSerial: boolean;
  @Input() localState: any;
  @Input() deactivateAdd: any;

  protected stockTakeName = 'Device Record Listing';
  protected readonly index = 4;

  private subSink = new SubSink;
  readonly quaggaStatus$ = this.componentStore.select(state => state.quaggaStatus);


  apiVisa: ApiVisa = AppConfig.apiVisa;

  gridApi: any;
  toggleColumn$: Observable<boolean>;
  searchModel = itemRecordSearchModel;
  sessionClosed = false;
  advSearchToggle = false;
  defaultColDef: Object = {
    filter: 'agTextColumnFilter',
    floatingFilter: false,
    floatingFilterComponentParams: { suppressFilterButton: true },
    minWidth: 170,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };
  gridOptions = {
    // [...]
    getRowStyle :  this.getRowStyleScheduled
  };

   getRowStyleScheduled(params) {
   if (params.data.state==='NEW' && params.data.valid==='VALIDATION IN PROGRESS') {
        return {
            'color': 'orange',
        }
   }

    if (params.data.valid==="INVALID") {
      return {
          'color': 'red',
      }
    }
};
  device = null;
  akaunMessageDialogComponentMatDialogRef: MatDialogRef<AkaunMessageDialogComponent>;

  @ViewChild(PaginationClientSideComponent) paginationComp: PaginationClientSideComponent;
  @ViewChild(AdvancedSearchComponentScan) advSearch: AdvancedSearchComponentScan;


  pagination = new Pagination();
 
  columnsDefs: Object[] = [
    {
      headerName: 'No.', field: 'index', cellRenderer: (params) => {
        return params.rowIndex + 1
      },
      minWidth: 60, width: 60, maxWidth: 90, type: 'numericColumn', floatingFilter: false, filter: false
    },
    { headerName: 'Item Code', field: 'code', cellStyle: { 'text-align': "left" }, minWidth: 110, maxWidth: 120, suppressSizeToFit: true },
    
    { headerName: 'Quantity', field: 'qty', type: 'numericColumn', minWidth: 80, maxWidth: 90, floatingFilter: false, suppressSizeToFit: true,
      valueGetter: function (params: any) {
        //console.log('params.data',params.data)
        let qty: number = params.data.qty;
        let ratio = params.data.ratio?params.data.ratio:1;
        
        if(ratio){
          qty = qty / ratio;
        }

        return qty;
      },
    },
    { headerName: 'UOM', field: 'uom', cellStyle: { 'text-align': "left" }, minWidth: 110, maxWidth: 120, suppressSizeToFit: true },
    { headerName: 'Record Status', field: 'valid', cellStyle: { 'text-align': "left" }, width: 110, suppressSizeToFit: true},
    { headerName: 'Item Name', field: 'name', cellStyle: { 'text-align': "left" }, width: 110, suppressSizeToFit: true},
    { headerName: 'Remarks', field: 'remarks', cellStyle: { 'text-align': "left" }, width: 110, suppressSizeToFit: true, editable: true },
    { headerName: 'SN# / Batch #', field: 'serial', cellStyle: { 'text-align': "left" }, minWidth: 110, maxWidth: 120, suppressSizeToFit: true },
    
    {
      headerName: 'Modified Date', field: 'date', 
      valueFormatter: (params) => {
        return params.value && params.value !== "None" ? moment(params.value).format('YYYY-MM-DD HH:mm:ss') : null;
      },
      sort: 'desc', cellStyle: { 'text-align': "left" }, suppressSizeToFit: true
        
    },
  
  ];
  form: FormGroup;
  invalid : boolean;
  serialExist : boolean;
  advForm: FormGroup;
  guids;
  s_box: string='';
  setCursor:boolean = false;
  // showAddButton:boolean = false;

  delimiter;
  item$:Observable<any []>;
  showMobileScan = false;
  hideAddButton = false;
  uomList$;
  selectedUOM;
  canAdd = false;
  selectedItem;
  isSerial = false;
  @ViewChild('codeFocus', { static: true }) codeInput: ElementRef;
  constructor(
    private toastr: ToastrService,
    private readonly store: Store<StockTakeStates>,
    private viewColFacade: ViewColumnFacade,
    private stockTakeSessionRecordService: StockTakeSessionRecordService,
    private subQueryService: SubQueryService,
    public dialog: MatDialog,
    private readonly componentStore: ComponentStore<LocalState>,
    private readonly sessionStore: Store<SessionStates>,
  ){
    super();
    this.componentStore.setState({
      quaggaStatus: 'idle',
    });
  }

  ngOnInit() {
    this.codeInput.nativeElement.focus();
    this.subSink.sink = this.sessionStore.select(SessionSelectorShared.selectMasterSettings).subscribe(
      (a: any) => {
        this.showMobileScan = a?.SCAN_CODE_MOBILE_VIEW;
        this.hideAddButton = a?.HIDE_ADD_BUTTON;
      })
    this.subSink.sink = this.store.select(ItemSelectors.selectInvalidItem)
    .pipe(
      debounceTime(500) 
    )
    .subscribe(res =>  {
      //console.log('res',res)
      if(res){
        this.canAdd = false;
        this.toastr.error("Code is invalid", "Error", {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 2000,
        });
      }else{
        this.canAdd = true;
      }
    })

    this.subSink.sink = this.store.select(ItemSelectors.selectItem)
      .pipe(
        filter(item => !!item), // only proceed if there's a valid item
        debounceTime(100) // optional: add small delay to avoid rapid firing
      )
      .subscribe(res => {
        this.selectedItem = res;

        if (this.hideAddButton) {
          // console.log('Valid item received. Calling onAdd()');
          this.onAdd();
        }
      });

    
    this.item$ = this.store.select(ItemSelectors.selectScannedItemList).pipe(map((i => i.filter(x=>x.status==='ACTIVE'))));
    this.item$ = this.item$.pipe(map((i => i.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()))))
    this.uomList$ = this.store.select(ItemSelectors.selectUomList);
    
    this.form = new FormGroup({
      scanned_code: new FormControl(''),
      itemCodeSearch: new FormControl(''),
      readers: new FormControl('code_128_reader'),
      remarks: new FormControl(''),
      uom: new FormControl(''),
      qty: new FormControl(1),
      batchSerialNumber: new FormControl(''),
    });

    this.store.select(ItemSelectors.selectItem).subscribe(res =>  {
      //console.log ("selectItem::::::::::::::::::",res);
      this.selectedItem = res;
      /* if(this.selectedItem){
        if(this.selectedItem.bl_inv_mst_item_hdr.sub_item_typ ){
          if(this.selectedItem.bl_inv_mst_item_hdr.sub_item_type==='SERIAL_NUMBER'){
            this.isSerial = true;
          }if(this.selectedItem.bl_inv_mst_item_hdr.sub_item_type==='BATCH_NUMBER'){
            this.isSerial = true;
          }else{
            this.isSerial = false;
          }
        }
      } */
     /*  if(this.selectedItem){
        this.form.patchValue({
          scanned_code: this.selectedItem.bl_inv_mst_item_hdr.code
        })
      } */
    }); 
     this.advForm = new FormGroup({
      itemCodeSearch: new FormControl(''),
      itemNameSearch: new FormControl(''),
    });
    this.uomList$.subscribe(uomList => {
      if (uomList.length === 1) {
        this.selectedUOM = uomList[0];
      }else if (uomList.length > 1) {
        const filteredList = uomList.filter(l=>l.scan_code !== '' && l.scan_code=== this.form.get('scanned_code').value?.trim());
        this.selectedUOM = filteredList[0];
      }
      this.form.patchValue({
        uom : this.selectedUOM
      })
    });
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.store.select(SessionDeviceSelectors.selectDevice).subscribe(device => {
      this.device = device.bl_inv_stock_take_session_device;
    })
    //to disable adding device if the session is closed
    this.store.select(SessionSelectors.selectSession).subscribe(session => {
      if (session.bl_inv_stock_take_session_hdr.status === "CLOSED") {
        this.sessionClosed = true;
      }
    });
  }



  // onAdd() {
  //   let scan_text = this.s_box;
  //   let arr:any; 
  //   arr = scan_text.split(this.delimiter, 2);
  //   this.store.dispatch(ItemActions.resetSerialGuids());
  //   this.store.dispatch(ItemActions.scanItemInit({code:arr}));
  //   this.s_box='';
  // }

  scanCode(e: string){
    //console.log ("scanCode::::::::::::::::::",e);
    let input = e.trim();
    this.model = new StockTakeItemRecordList();
    this.model.code = input;
    this.model.name = '';
    this.model.guid = UUID.UUID().toLowerCase();
    this.model.date = Date.now();
    this.model.record= null;
    this.model.valid = "IN PROGRESS";
    this.model.state = "NEW";
    this.model.status = "ACTIVE";
    this.model.serial = "";
    this.model.qty = 1;
    //console.log ("scanCode::::::::::::::::::", this.model);


    this.store.dispatch(ItemActions.checkValidItem({model: this.model}));
  }

  disableAdd(){
   let result = false;
   
   if(!this.canAdd){
    result = true;
   }
   if(this.sessionClosed){
    result = true;
   }
   return result;
  }

  onAdd(){
    const ratio =  this.selectedUOM.ratio?this.selectedUOM.ratio:1; 
    const uom =  this.selectedUOM.uom?this.selectedUOM.uom:""; 
    //console.log('ratio',ratio)
    //console.log('uom',uom)
    this.model.qty = this.form.value.qty;
    this.model.qtyUom = this.form.value.qty;
    this.model.qty = this.model.qty * ratio;
    this.model.uom = uom;
    this.model.ratio = ratio;
    this.model.remarks = this.form.value.remarks;

    if(this.selectedItem){
      //console.log('this.selectedItem',this.selectedItem)
      if(
        this.form.value.batchSerialNumber 
        && ( 
        (this.selectedItem.bl_inv_mst_item_hdr.sub_item_type && this.selectedItem.bl_inv_mst_item_hdr.sub_item_type!=='SERIAL_NUMBER')
        && 
        (this.selectedItem.bl_inv_mst_item_hdr.sub_item_type && this.selectedItem.bl_inv_mst_item_hdr.sub_item_type!=='BATCH_NUMBER'))
      ){
        this.toastr.error("Item is not serialized or batch & expiry type", "Error", {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 2000,
        });
        this.isSerial = false;
      }else{
        this.model.serial = this.form.value.batchSerialNumber;
        this.store.dispatch(ItemActions.scanItemToListing({model: this.model}));
        this.store.dispatch(ItemActions.addItemRecord({model: this.model}));
        this.form.controls['scanned_code'].patchValue(null);
        this.form.controls['batchSerialNumber'].patchValue(null);
        this.form.controls['remarks'].patchValue(null);
        this.form.controls['uom'].patchValue(null);
        this.form.controls['qty'].patchValue(1);
        this.store.dispatch(ItemActions.resetUOMList());
      }
     
    }
    
    this.codeInput.nativeElement.focus();
  }

  onSelectUOM(e){
    this.selectedUOM = e;
  }

  onScanSerialBatch(e: string){
     //console.log('onScanSerialBatch',this.selectedItem)
     if(this.selectedItem){
      if(this.selectedItem.bl_inv_mst_item_hdr.sub_item_type && this.selectedItem.bl_inv_mst_item_hdr.sub_item_type==='SERIAL_NUMBER' ){
        this.store.dispatch(ItemActions.scanSerialNumber({serial: e}));
        this.isSerial = true;
      }else if(this.selectedItem.bl_inv_mst_item_hdr.sub_item_type && this.selectedItem.bl_inv_mst_item_hdr.sub_item_type==='BATCH_NUMBER'){
        console.log('BATCH_NUMBER',this.selectedItem)
        this.store.dispatch(ItemActions.scanBatchNumber({batch: e}));
        this.isSerial = true;
      }else{
        this.toastr.error("Item is not serialized or batch & expiry type", "Error", {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 2000,
        });
        this.isSerial = false;
        this.form.controls['batchSerialNumber'].patchValue(null);
      }
     }else{
      this.store.dispatch(ItemActions.scanSerialBatch({code: e}));
     }  
  }
  
  onEnter(e, isScanner) {
    let input = e.trim();
    //console.log ("input::::::::::::::::::",e);
    if(isScanner && this.advSearch?.basicSearchQuery){
      input = this.advSearch?.basicSearchQuery+input;
    }
    let arr = input.split('*',"2");

    this.model = new StockTakeItemRecordList();
    if(arr.length>1){
      this.model.code = arr[1];
      this.model.qty = arr[0];
    }else{
      this.model.code = input;
      this.model.qty = 1;
    }
    //console.log ("this.modelll 1",this.model);
    this.model = new StockTakeItemRecordList();
    this.model.code = input;
    this.model.name = '';
    this.model.qty = 1;
    this.model.guid = UUID.UUID().toLowerCase();
    this.model.date = Date.now();
    this.model.record= null;
    this.model.valid = "VALIDATION IN PROGRESS";
    this.model.state = "NEW";
    this.model.status = "ACTIVE";
    this.model.serial = "";
    this.model.remarks = this.form.value.remarks;
    //console.log ("this.modelll 2",this.model);
    this.store.dispatch(ItemActions.scanItemToListing({model: this.model}));
    this.store.dispatch(ItemActions.checkValidItem({model: this.model}));
    this.advSearch.basicSearchQuery = "";
    this.form.controls['remarks'].patchValue(null);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.retrieveData([this.setCriteria('calcTotalRecords', 'true')]);
  }

  onNext() {
    if (this.device.status === 'INACTIVE') {
      this.akaunMessageDialogComponentMatDialogRef = this.dialog.open(AkaunMessageDialogComponent, { width: '400px' });
      this.akaunMessageDialogComponentMatDialogRef.componentInstance.confirmMessage = 'This Device is Inactive, Cannot scan items';
    }
    else {
      this.viewColFacade.updateInstance(this.index,
        {
          ...this.localState,
          deactivateReturn: true,
          deactivateAdd: true,
          deactivateList: true,
        });
      this.viewColFacade.onNext(6);
    }
  }

  onRowClicked(event: any) {
  const clickedElement = event.event.target;
  const cellElement = clickedElement.closest('.ag-cell');


  if (cellElement) {
    const columnId = cellElement.getAttribute('col-id');
    if (columnId === 'remarks') {
      return;
    } else{
      if(event.data.record){
        
        this.store.dispatch(DeviceRecordActions.resetSelectedRecord());
        //this.store.dispatch(DeviceRecordActions.loadSelectedDeviceRecord({ deviceRecord: event.data.record }));
        this.store.dispatch(ItemActions.selectItemCode({ guid: event.data.record.bl_inv_stock_take_session_record.guid }));
        if (!this.localState.deactivateList) {
          this.viewColFacade.updateInstance(this.index,
            {
              ...this.localState,
              deactivateReturn: true,
              deactivateAdd: true,
              deactivateList: true
            });
          this.viewColFacade.onNext(8);
        }
      }
    }
  }
    
    
  }

  clear() {
    let dataSource = {
      getRows(params: any) {
        params.successCallback([], 0);
      }
    };
    this.gridApi.setServerSideDatasource(dataSource);
  }

  pageFiltering(filterModel) {
    var noFilters = Object.keys(filterModel).length <= 0;

    if (noFilters)
      return {
        by: (viewModel) => true,
        isFiltering: noFilters
      }

    return {
      by: (viewModel) => Object
        .keys(filterModel)
        .map(col => {
          let newCol = col.split("."),
            tableColVal = viewModel.bl_inv_stock_take_session_record[`${newCol[1]}`];

          if (typeof tableColVal == "number")
            return (tableColVal === Number(filterModel[`${col}`].filter));
          else
            return (tableColVal.toLowerCase().includes(filterModel[`${col}`].filter.toLowerCase()));
        })
        .reduce((p, c) => p && c),
      isFiltering: noFilters
    }
  }

  pageSorting(sortModel) {
    return (data) => { 
      if (sortModel.length <= 0)
        return data;

      let newData = data;
      sortModel.forEach(model => {
        let col = model.colId.split(".");
        newData = model.sort === 'asc' ?
          newData.sort((p, c) => 0 - (p[col[0]][col[1]] > c[col[0]][col[1]] ? -1 : 1)) :
          newData.sort((p, c) => 0 - (p[col[0]][col[1]] > c[col[0]][col[1]] ? 1 : -1))
      })
      return newData;
    }
  }

  searchQuery(query: string, table: string) {
    let flag = true;
    this.subSink.sink = this.store.select(SessionSelectors.selectGuid).subscribe(
      (guid) => {
        this.subSink.sink = this.store.select(SessionDeviceSelectors.selectGuid).subscribe(
          (deviceGuid) => {
            if (guid && flag) {
              flag = false;
              let querySessionHeaderCondition = ` AND guid_session_hdr = '${guid}'`;
              querySessionHeaderCondition += ` AND guid_session_device = '${deviceGuid}'`;

              var query$ = this.subQueryService
                .post({ 'subquery': query + querySessionHeaderCondition, 'table': table }, this.apiVisa)
                .pipe(
                  switchMap(res => of(res))
                );
              query$.pipe(
                filter((res: ApiResponseModel<any>) => res.data.length > 0))
                .subscribe(res => {
                  //console.log("guids", res.data)
                  if(res.data.length!==0 || res.data.length<=50){
                    this.store.dispatch(DeviceRecordActions.loadDeviceRecordInit2({guids:res.data}))
                  }else{
                    this.toastr.error("Result Set Too Large. Please Refine Search", "Error", {
                      tapToDismiss: true,
                      progressBar: true,
                      timeOut: 2000,
                    });
                  }
                });
              query$.pipe(
                filter((res: ApiResponseModel<any>) => res.data.length === 0))
                .subscribe(res => {
                  this.clear()
                });
            }
          },
          (err) => {
            console.error("SessionSelectors.selectGuid", err);
          }

        );
      },
      (err) => {
        console.error("SessionDeviceSelectors.selectGuid", err);
      }
    )
  }

  onSearch(e: SearchQueryModel) {
    //console.log('onSearch',e)
    !e.isEmpty ? this.searchQuery(e.queryString, e.table) : this.store.dispatch(DeviceRecordActions.loadDeviceRecordInit2({guids:null}));
  }

  retrieveData(criteria) {
    if (criteria) {
      const datasource = {
        getRows: this.getRowsFactory(criteria)
      };
      this.gridApi.setServerSideDatasource(datasource);
    }
  }

  getRowsFactory(criteria) {
    let callbackTotal = 0;
    let callback = false;
    let temp_criteria = null;
    let offset = 0;
    // let limit = this.paginationComponent.rowPerPage;
    let limit = 100;

    return grid => {
      var filter = this.pageFiltering(grid.request.filterModel);
      var sortOn = this.pageSorting(grid.request.sortModel);

      if (!Object.keys(grid.request.filterModel).length) {
        offset = grid.request.startRow;
        limit = grid.request.endRow - offset;
        if (criteria.find(x => x.columnName === 'guids')?.value.length > 100) {
          callbackTotal = criteria.find(x => x.columnName === 'guids')?.value.length;
          callback = true;
          let guids = criteria.find(x => x.columnName === 'guids').value.slice(offset, limit);
          temp_criteria = [
            this.setCriteria("guids", guids),
            this.setCriteria("calcTotalRecords", 'true')
          ];
        }
      }

      this.store.dispatch(DeviceRecordActions.loadDeviceRecordInit({ request: grid.request }));
      this.subSink.sink = this.store.select(SessionDeviceSelectors.selectGuid).subscribe(deviceGuid => {
        if (deviceGuid) {
          this.subSink.sink = this.store.select(SessionSelectors.selectGuid).subscribe(
            (guid) => {

              let sql = [...temp_criteria ? temp_criteria : criteria].map((criteria) => {
                if (criteria.columnName !== "guid_session_hdr") {
                  return criteria;
                }
              })
              sql.push(this.setCriteria('guid_session_hdr', guid));
              sql.push(this.setCriteria('guid_session_device', deviceGuid));

              this.subSink.sink = this.stockTakeSessionRecordService.getByCriteria(
                new Pagination(
                  offset,
                  limit,
                  sql,
                  [
                    { columnName: 'orderBy', value: 'date_lastupdated' },
                    { columnName: 'order', value: 'DESC' },
                  ]
                ),
                this.apiVisa)
                .subscribe(
                  (res) => {

                    const data = sortOn(res.data.filter(o => filter.by(o)));
                    let totalRecords;
                    if (!callback) {
                      totalRecords = filter.isFiltering ? res.totalRecords : data.length;
                    }
                    else {
                      totalRecords = callbackTotal;
                    }

                    grid.successCallback(data, totalRecords);
                    this.store.dispatch(DeviceRecordActions.loadDeviceRecordSuccess({ totalRecords }));
                  }, (err) => {
                    grid.failCallback();
                    this.store.dispatch(DeviceRecordActions.loadDeviceRecordFailure({ error: err.message }));
                  });
            },
            (err) => {
              console.error("SessionSelectors.selectGuid", err);

            })
        }
      },
        (err) => {
          console.error("SessionDeviceSelectors.selectGuid", err);
        });
    }
  }
  
  setCriteria(columnName, value) {
    return { columnName, operator: '=', value }
  }

  onScan() {
    this.componentStore.patchState(state => ({ ...state, quaggaStatus: 'initializing' }));
    this.form.controls['readers'].disable();
    this.setCursor = true;
    Quagga.init({
      inputStream: {
        name: 'Live',
        type: 'LiveStream',
        target: document.querySelector('#camera')
      },
      decoder: {
        readers: [this.form.getRawValue().readers]
      }
    }, (err) => {
      if (err) {
        console.error(err);
        return;
      }
      $('video').css({
        'width': '100%',
        'border': 'mediumslateblue 4px solid',
        'border-radius': '10px'
      });
      $('canvas').remove();
      $('#camera').show();
      this.componentStore.patchState(state => ({ ...state, quaggaStatus: 'ready' }));
      Quagga.start();
    });
    // this.showAddButton = true;
    let currentCode = '';
    let timeout = null;
    Quagga.onDetected((data) => {
      //console.log('onScan-DETECT');
      this.componentStore.patchState(state => ({
        ...state,
      }));

      if (currentCode !== data.codeResult.code) {
        currentCode = data.codeResult.code;
        this.beep();

        this.onEnter(currentCode, true);
      }
      
      clearTimeout(timeout)
      timeout = setTimeout(function () {
        currentCode = ''
      }, 500)
      // $('#camera').hide();
      // this.componentStore.patchState(state => ({ ...state, quaggaStatus: 'idle' }));
      // Quagga.stop();
      // console.log('onScan-STOP-Automatically');
      // this.form.controls['readers'].enable();
    });
  }

  onStop() {
    $('#camera').hide();
    this.componentStore.patchState(state => ({ ...state, quaggaStatus: 'idle' }));
    Quagga.stop();
    //console.log('onScan-STOP-by Button');
    this.form.controls['readers'].enable();
    // this.showAddButton = false;
  }
  
  beep() {
    var context = new AudioContext();
    var oscillator = context.createOscillator();
    oscillator.type = "sine";
    oscillator.frequency.value = 800;
    oscillator.connect(context.destination);
    oscillator.start(); 
    // Beep for 500 milliseconds
    setTimeout(function () {
        oscillator.stop();
    }, 500);
  }


  onCellValueChanged(event: any) {
    const updatedValue = event.newValue;
    this.store.dispatch(ItemActions.updateRecordsRemarks({guid: event.data.guid,newValue:updatedValue}));
  }

  ngOnDestroy() {
    this.subSink.unsubscribe();
  }

}