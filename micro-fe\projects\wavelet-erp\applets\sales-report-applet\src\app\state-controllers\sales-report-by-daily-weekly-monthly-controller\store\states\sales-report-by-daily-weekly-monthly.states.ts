import { createEntityAdapter } from '@ngrx/entity';

export interface SalesReportByDailyWeeklyMonthlyState {
  totalRecords: number;
  errorLog: { timeStamp: Date, log: string }[];
  rowData: [];
}

export const adapter = createEntityAdapter<any>({
  selectId: a => a.period
});

export const initState: SalesReportByDailyWeeklyMonthlyState = adapter.getInitialState({
  totalRecords: 0,
  errorLog: [],
  rowData: []
});
