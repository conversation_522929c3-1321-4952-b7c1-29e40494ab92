
// Angular Core
import { ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

// NgRx
import { Store } from "@ngrx/store";
import { ComponentStore } from '@ngrx/component-store';

// Third-party Libraries
import { GridOptions } from 'ag-grid-enterprise';
import { ErrorMessages } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { Observable, from } from 'rxjs';
import { map, mergeMap, take, toArray } from 'rxjs/operators';
import { SubSink } from 'subsink2';

// Session Controller
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';

// Permissions
import { ClientSidePermissionsSelectors } from "projects/shared-utilities/modules/permission/client-side-permissions-controller/selectors";
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';

// Shared Utilities
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import * as moment from 'moment';

// Application Imports
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { SalesReportByDailyWeeklyMonthlySearchModel } from '../../../models/advanced-search-models/sales-report-by-daily-weekly-monthly-search.model';
import { SalesReportByItemCodeInputModel, SalesReportByItemCodeModel } from '../../../models/sales-report-by-item-code-model';
import { ApiService } from '../../../services/api-service';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-sales-report-by-daily-weekly-monthly',
  templateUrl: './sales-report-by-daily-weekly-monthly.component.html',
  styleUrls: ['./sales-report-by-daily-weekly-monthly.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class SalesReportByDailyWeeklyMonthlyComponent extends ViewColumnComponent implements OnInit, OnDestroy {

  protected subs = new SubSink();

  compId = 'salesReportByDailyWeeklyMonthly';
  compName = 'Sales Report By Daily Weekly Monthly';
  protected readonly index = 0;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  protected localState: LocalState;

  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  toggleColumn$: Observable<boolean>;
  searchModel = SalesReportByDailyWeeklyMonthlySearchModel;

  showColumns = [
    { name: 'sales_cost', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' }
  ]

  rowData = [];
  totalRecords = 0;
  salesCost = 'cost_ma';
  optional = [];
  branchGuids = [];

  // Period-related properties
  selectedPeriod: 'day' | 'week' | 'month' = 'month';
  dynamicColumns: any[] = [];
  baseColumnDefs: any[] = [];
  periodMap: Record<string, 'DAILY' | 'WEEKLY' | 'MONTHLY'> = {
    day: 'DAILY',
    week: 'WEEKLY',
    month: 'MONTHLY'
  };

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;

  gridOptions: GridOptions = {
    pagination: false,
    groupIncludeTotalFooter: true,
  };

  columnsDefs = [
    {
      headerName: 'Category 1',
      field: 'category1_code',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 2',
      field: 'category2_code',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 3',
      field: 'category3_code',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 4',
      field: 'category4_code',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 5',
      field: 'category5_code',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 6',
      field: 'category6_code',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 7',
      field: 'category7_code',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 8',
      field: 'category8_code',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 9',
      field: 'category9_code',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Category 10',
      field: 'category10_code',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Item Code',
      field: 'item_code',
      type: 'textColumn'
    },
    {
      headerName: 'Item Name',
      field: 'item_name',
      type: 'textColumn'
    },
    {
      headerName: 'Type',
      field: 'type',
      type: 'textColumn'
    },
    {
      headerName: 'UOM',
      field: 'uom',
      type: 'textColumn'
    },
    {
      headerName: 'Qty',
      field: 'qty_sold',
      type: 'integerColumn'
    },
    {
      headerName: 'Amount',
      field: 'sales_amount',
      type: 'decimalColumn'
    },
    {
      headerName: 'Sales Cost',
      field: 'sales_cost',
      type: 'decimalColumn'
    },
    {
      headerName: 'GP',
      field: 'gp',
      type: 'decimalColumn'
    },
  ];

  readPermissionDefintion = {
    branch : 'API_TNT_DM_ERP_SALES_REPORT_BY_ITEM_CODE_READ'
  }

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected apiService: ApiService,
    protected toastr: ToastrService,
    private readonly permissionStore: Store<PermissionStates>,
    private readonly sessionStore: Store<SessionStates>,
    private readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {

    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.viewColFacade.rowData$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.viewColFacade.totalRecords$.subscribe(totalRecords => this.totalRecords = totalRecords);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });

    this.branchGuids = UtilitiesModule.getTargetsByPermission(this.userPermissionTarget$,
      this.readPermissionDefintion.branch, 'bl_fi_mst_branch'
    );

    // Store base column definitions
    this.baseColumnDefs = [...this.columnsDefs];

    // Initialize with default period (month)
    this.generateDynamicColumns('month', moment().startOf('month').toDate(), moment().endOf('month').toDate());
  }

  onNext() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false
    });
    this.viewColFacade.onNextAndReset(this.index, 1);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
    else
    {
      //this.createData();
    }

    const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
    statusBarComponent.setTitle(this.compName);
    statusBarComponent.setFilename(this.compName);
    statusBarComponent.setHideGroupColumn(true);
  }

  createData(inputModel?: SalesReportByItemCodeInputModel) {
    this.clear();
    this.viewColFacade.loadInit(this.gridApi);
    this.subs.sink = this.apiService.getSalesReportByDailyWeeklyMonthly(inputModel, this.apiVisa).pipe(
      mergeMap(a => from(a.data).pipe(
        map(b => {
          const sales_cost = this.getBaseAmt(b, this.getSalesCost(b));
          const sales_amount = this.getBaseAmt(b, b.sales_amount);

          Object.assign(b,
            {
              sales_cost: sales_cost,
              sales_amount: sales_amount,
              gp: sales_amount - sales_cost,
            }
          )
          return b;
        }),
        toArray(),
        map(c => {
          a.data = c;
          return a;
        })
      ))
    ).subscribe(resolved => {
      //console.log(resolved);
      this.viewColFacade.loadSuccess(resolved);

      // Group data by item and aggregate totals
      const groupedData = this.groupDataByItem(resolved.data);
      this.rowData = this.checkDataRow(groupedData);
      this.totalRecords = groupedData.length;
      this.gridApi.setRowData(this.rowData);
      this.viewColFacade.selectRowData(this.rowData);
      this.viewColFacade.selectTotalRecords(this.totalRecords);
    }, err => {
      console.error(err);
      let msg = err.status===403 ? ErrorMessages.CRUD_403_ERRORS.READ : err.message;
      this.toastr.error(
        msg,
        "AG Gird Error",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      this.viewColFacade.loadFailed(err);
    });
  };

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onSearch(e: SearchQueryModel) {
    console.log("search", e);
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3)  {
        this.toastr.error(
          'Search keyword must more than 2 characters.',
          'Keyword',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      }

      const inputModel = {} as SalesReportByItemCodeInputModel;
      inputModel.keyword = e.keyword;
      inputModel.date_from = '2022-01-01T00:00:00.000Z';
      inputModel.date_to = '2099-12-31T00:00:00.000Z';
      inputModel.branch_guids = this.branchGuids ?? [];
      
      if (e.queryString) {
        inputModel.keyword = UtilitiesModule.checkNull(e.queryString['itemCode'],'');
        inputModel.branch_guids = UtilitiesModule.checkNull(e.queryString['branch'],[]);
        inputModel.date_from = UtilitiesModule.checkNull(e.queryString['date']['from'],'2022-01-01T00:00:00.000Z');
        inputModel.date_to = UtilitiesModule.checkNull(e.queryString['date']['to'],'2099-12-31T00:00:00.000Z');

        // Handle period selection for dynamic columns
        const period = UtilitiesModule.checkNull(e.queryString['date']['period'], 'month');
        //[WEEKLY, MONTHLY, DAILY]
        if (period) {
          this.selectedPeriod = period;
          const fromDate = new Date(inputModel.date_from);
          const toDate = new Date(inputModel.date_to);
          this.generateDynamicColumns(period, fromDate, toDate);

          const newPeriod: 'DAILY' | 'WEEKLY' | 'MONTHLY' = this.periodMap[period] || 'MONTHLY';
          inputModel.report_period = newPeriod;
        }

        inputModel.customer_guids = UtilitiesModule.checkNull(e.queryString['customer'],[]);
        inputModel.salesman_guids = UtilitiesModule.checkNull(e.queryString['salesman'],[]);
        inputModel.item_type = UtilitiesModule.checkNull(e.queryString['itemType'],[]);
        
        const itemStatus = UtilitiesModule.checkNull(e.queryString['itemStatus'], []);
        inputModel.item_status = Array.isArray(itemStatus) ? itemStatus : [itemStatus];

        inputModel.item_category1_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel1'],[]);
        inputModel.item_category2_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel2'],[]);
        inputModel.item_category3_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel3'],[]);
        inputModel.item_category4_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel4'],[]);
        inputModel.item_category5_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel5'],[]);
        inputModel.item_category6_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel6'],[]);
        inputModel.item_category7_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel7'],[]);
        inputModel.item_category8_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel8'],[]);
        inputModel.item_category9_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel9'],[]);
        inputModel.item_category10_guids =UtilitiesModule.checkNull( e.queryString['itemCategoryLevel10'],[]);
      
        this.salesCost = UtilitiesModule.checkNull(e.queryString['calculateBaseOn'],'');

      }

      //console.log('inputModel', inputModel);
      this.createData(inputModel);

      const date_from = UtilitiesModule.dateFormatter(inputModel.date_from);
      const date_to = UtilitiesModule.dateFormatter(inputModel.date_to);
      // Set branch names asynchronously
      this.setBranchSubTitle(inputModel.branch_guids, date_from, date_to);
    }
    else {
      //this.createData();
    }
  }

  clear() {
    this.gridApi.setRowData([]);
    this.totalRecords = 0;
    this.rowData = [];
  }

  groupDataByItem(data: any[]): any[] {
    const groupedMap = new Map();

    data.forEach(item => {
      const itemKey = item.fi_item_guid || item.item_code;

      if (!groupedMap.has(itemKey)) {
        // Create base item structure with totals
        groupedMap.set(itemKey, {
          ...item,
          qty_sold: 0,
          sales_amount: 0,
          sales_cost: 0,
          gp: 0,
          gp_percentage: 0,
          period_data: [] // Store all period data for this item
        });
      }

      const groupedItem = groupedMap.get(itemKey);

      // Add to period data array
      groupedItem.period_data.push(item);

      // Aggregate totals
      const quantity = item.quantity_base || 0;
      const amount = item.amount_net || 0;
      const cost = item.cost_ma_amount || 0;

      groupedItem.qty_sold += quantity;
      groupedItem.sales_amount += amount;
      groupedItem.sales_cost += cost;
      groupedItem.gp += (amount - cost);
    });

    // Convert map to array and calculate percentages
    const result = Array.from(groupedMap.values()).map(item => {
      if (item.sales_amount !== 0) {
        item.gp_percentage = (item.gp / item.sales_amount) * 100;
      }
      return item;
    });

    return result;
  }

  generateDynamicColumns(period: 'day' | 'week' | 'month', fromDate: Date, toDate: Date) {
    this.dynamicColumns = [];

    const from = moment(fromDate);
    const to = moment(toDate);

    switch (period) {
      case 'day':
        let currentDay = from.clone();
        while (currentDay.isSameOrBefore(to, 'day')) {
          const dayKey = currentDay.format('YYYY-MM-DD');
          const dayLabel = currentDay.format('YYYY-MM-DD');

          // Create grouped column with sub-columns for Qty, Amount, GP
          this.dynamicColumns.push({
            headerName: dayLabel,
            headerClass: 'ag-center-aligned-header',
            children: [
              {
                headerName: 'Qty',
                field: `day_${dayKey}_qty`,
                type: 'integerColumn',
                valueGetter: (params) => this.getPeriodValue(params.data, dayKey, 'qty')
              },
              {
                headerName: 'Amount',
                field: `day_${dayKey}_amount`,
                type: 'decimalColumn',
                valueGetter: (params) => this.getPeriodValue(params.data, dayKey, 'amount')
              },
              {
                headerName: 'GP',
                field: `day_${dayKey}_gp`,
                type: 'decimalColumn',
                valueGetter: (params) => this.getPeriodValue(params.data, dayKey, 'gp')
              }
            ]
          });

          currentDay.add(1, 'day');
        }
        break;

      case 'week':
        let currentWeek = from.clone().startOf('isoWeek');
        while (currentWeek.isSameOrBefore(to, 'day')) {
          const weekNumber = currentWeek.format('WW');
          const weekKey = currentWeek.format('YYYY-') + weekNumber;
          const weekLabel = ` ${currentWeek.format('YYYY')}-W${weekNumber}`;

          // Create grouped column with sub-columns for Qty, Amount, GP
          this.dynamicColumns.push({
            headerName: weekLabel,
            headerClass: 'ag-center-aligned-header',
            children: [
              {
                headerName: 'Qty',
                field: `week_${weekKey}_qty`,
                type: 'integerColumn',
                valueGetter: (params) => this.getPeriodValue(params.data, weekKey, 'qty')
              },
              {
                headerName: 'Amount',
                field: `week_${weekKey}_amount`,
                type: 'decimalColumn',
                valueGetter: (params) => this.getPeriodValue(params.data, weekKey, 'amount')
              },
              {
                headerName: 'GP',
                field: `week_${weekKey}_gp`,
                type: 'decimalColumn',
                valueGetter: (params) => this.getPeriodValue(params.data, weekKey, 'gp')
              }
            ]
          });

          currentWeek.add(1, 'week');
        }
        break;

      case 'month':
        let currentMonth = from.clone().startOf('month');
        while (currentMonth.isSameOrBefore(to, 'month')) {
          const monthKey = currentMonth.format('YYYY-MM');
          const monthLabel = currentMonth.format('MMMM YYYY');

          // Create grouped column with sub-columns for Qty, Amount, GP
          this.dynamicColumns.push({
            headerName: monthLabel,
            headerClass: 'ag-center-aligned-header',
            children: [
              {
                headerName: 'Qty',
                field: `month_${monthKey}_qty`,
                type: 'integerColumn',
                valueGetter: (params) => this.getPeriodValue(params.data, monthKey, 'qty')
              },
              {
                headerName: 'Amount',
                field: `month_${monthKey}_amount`,
                type: 'decimalColumn',
                valueGetter: (params) => this.getPeriodValue(params.data, monthKey, 'amount')
              },
              {
                headerName: 'GP',
                field: `month_${monthKey}_gp`,
                type: 'decimalColumn',
                valueGetter: (params) => this.getPeriodValue(params.data, monthKey, 'gp')
              }
            ]
          });

          currentMonth.add(1, 'month');
        }
        break;
    }

    // Update column definitions with base columns + dynamic columns
    this.columnsDefs = [...this.baseColumnDefs, ...this.dynamicColumns];

    // Update grid columns if grid is ready
    if (this.gridApi) {
      this.gridApi.setColumnDefs(this.columnsDefs);
    }
  }

  getPeriodValue(data: any, periodKey: string, valueType: 'qty' | 'amount' | 'gp'): number {
    if (!data || !data.period_data || !Array.isArray(data.period_data)) {
      return 0;
    }

    // Filter data items that match the period key
    const periodItems = data.period_data.filter(item => {
      if (!item.period) return false;

      // Handle different period formats
      switch (this.selectedPeriod) {
        case 'day':
          return item.period === periodKey; // Format: 2025-07-11
        case 'week':
          return item.period === periodKey; // Format: 2025-46
        case 'month':
          return item.period === periodKey; // Format: 2025-07
        default:
          return false;
      }
    });

    // Aggregate values based on type
    let total = 0;
    periodItems.forEach(item => {
      switch (valueType) {
        case 'qty':
          total += item.quantity_base || 0;
          break;
        case 'amount':
          total += item.amount_net || 0;
          break;
        case 'gp':
          const amount = item.amount_net || 0;
          const cost = item.cost_ma_amount || 0;
          total += (amount - cost);
          break;
      }
    });

    return total;
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  setDataRowCache() {
    //console.log('set data row cache');
    this.gridApi.setRowData(this.rowData);
  }

  getSalesCost(model: SalesReportByItemCodeModel) {
    return model[this.salesCost + '_amount'];
  }

  calculateTotal(node, key) {
    if (node.group) {
      let totalCost = 0;
      const children = node.childrenAfterGroup;

      children.forEach((childNode) => {
        const childCost = this.calculateTotal(childNode, key);
        totalCost += childCost;
      });

      return totalCost;
    } else {
      const rowData = node.data;
      return rowData && rowData[key] ? rowData[key] : 0;
    }
  }

  isHideGLCodeType() {
    return (this.optional.indexOf('HIDE_GL_CODE_TYPE') >= 0);
  }

  checkDataRow(rowData) {
    if (this.isHideGLCodeType()) {
      rowData = rowData.filter(item => item.type !== 'GL_CODE');
    }
    return rowData;
  }

  getBaseAmt(data, value) {
    if (!value || value === 0) return 0;

    if (this.isForex(data)) {
      return value / (data?.base_doc_xrate || 1);
    }
    else {
      return value;
    }
  }

  isForex(data): boolean {
    return data?.doc_ccy && data?.base_doc_ccy && data?.doc_ccy !== data?.base_doc_ccy && data?.base_doc_xrate > 0;
  }

  setBranchSubTitle(guids: string[], dateFrom: string, dateTo: string) {
    const key = 'branch';

    this.sessionStore.select(SessionSelectors.selectDropDownRowData).subscribe(resolved => {
      let rowData = resolved ? resolved[key] ? resolved[key] : [] : [];
      let branchText = "ALL BRANCHES";

      if (rowData.length > 0 && guids && guids.length > 0) {
        const branches = rowData.filter(row => guids.includes(row.guid));
        if (branches.length > 0) {
          branchText = branches.map(branch => `${branch.name} (${branch.code})`).join(', ');
        }
      }

      let subTitle = branchText;
      subTitle += "\n";
      subTitle += `${dateFrom} to ${dateTo}`;

      const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
      statusBarComponent.setSubTitle(subTitle);
    });
  }
  
}

