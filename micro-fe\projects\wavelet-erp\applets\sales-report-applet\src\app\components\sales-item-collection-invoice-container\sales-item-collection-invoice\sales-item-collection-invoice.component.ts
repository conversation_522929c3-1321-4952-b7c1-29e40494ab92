// Angular Core
import { ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

// NgRx
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';

// Third-party Libraries
import { GridOptions } from 'ag-grid-enterprise';
import { DocumentShortCodesClass, ErrorMessages } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { Observable, from } from 'rxjs';
import { filter, map, mergeMap, take, toArray } from 'rxjs/operators';
import { SubSink } from 'subsink2';

// Shared Utilities - Models
import { ListingInputModel } from 'projects/shared-utilities/models/listing-input.model';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';

// Shared Utilities - Permissions
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';

// Shared Utilities - Session
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';

// Shared Utilities - Components & Utilities
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';

// Application Imports
import { ApiService } from '../../../services/api-service';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { SalesItemCollectionInvoiceSearchModel } from '../../../models/advanced-search-models/sales-item-collection-invoice-search.model';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-sales-item-collection-invoice',
  templateUrl: './sales-item-collection-invoice.component.html',
  styleUrls: ['./sales-item-collection-invoice.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class SalesItemCollectionInvoiceComponent extends ViewColumnComponent implements OnInit, OnDestroy {

  protected subs = new SubSink();

  compId = 'salesItemCollectionInvoice';
  compName = 'Sales Item and Collection Invoice';
  protected readonly index = 0;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  protected localState: LocalState;

  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  toggleColumn$: Observable<boolean>;
  searchModel = SalesItemCollectionInvoiceSearchModel;

  showColumns = [
    { name: 'unit_cost', setting: 'HIDE_UNIT_COST', permission: 'SHOW_UNIT_COST' },
    { name: 'total_cost', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' }
  ]

  branchGuids: string[] = [];
  rowData = [];
  totalRecords = 0;
  salesCost = 'cost_ma';
  subTitle = '';
  searchQuery: SearchQueryModel;

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;

  gridOptions: GridOptions = {
    pagination: false,
    groupIncludeTotalFooter: true
  };

  columnsDefs = [
    { 
      headerName: 'Invoice', 
      field: 'inv',
      type: 'textColumn'
    },
    {
      headerName: 'SO',
      field: 'so',
      type: 'textColumn'
    },
    {
      headerName: 'Branch',
      field: 'code_branch',
      type: 'textColumn'
    },
    {
      headerName: 'Date',
      field: 'date_txn',
      type: 'dateColumn'
    },
    {
      headerName: 'Salesman',
      field: 'sales_entity_hdr_name',
      type: 'textColumn'
    },
    {
      headerName: 'Salesman Code',
      field: 'sales_entity_hdr_code',
      type: 'textColumn'
    },
    {
      headerName: 'Item Code',
      field: 'line_item_code',
      type: 'textColumn',
    },
    {
      headerName: 'Item Name',
      field: 'line_item_name',
      type: 'textColumn'
    },
    {
      headerName: 'Sold', 
      field: 'line_quantity_base',
      type: 'integerColumn'
    },
    {
      headerName: 'Sales Return',
      field: 'sr',
      type: 'textColumn'
    },
    {
      headerName: 'Unit Net Price',
      field: 'unit_price_net',
      type: 'decimalColumn'
    },
    {
      headerName: 'Tax Amount',
      field: 'line_amount_tax_gst',
      type: 'decimalColumn',
    },
    {
      headerName: 'Net Price (A)',
      field: 'line_amount_txn',
      type: 'decimalColumn'
    },
    {
      headerName: 'Net Price Exclude Tax',
      field: 'net_price_exclude_tax',
      type: 'decimalColumn'
    },
    {
      headerName: 'Fin Cost (B)',
      field: 'fin_cost',
      type: 'decimalColumn'
    },
    {
      headerName: 'Fin Rate (B/A)',
      field: 'fin_rate',
      type: 'textColumn',
      cellStyle: { textAlign: 'right' }
    },
    {
      headerName: 'Unit Cost (MA)',
      field: 'unit_cost',
      type: 'decimalColumn'
    },
    {
      headerName: 'Total Cost (MA)',
      field: 'total_cost',
      type: 'decimalColumn'
    },
    {
      headerName: 'Total Margin',
      field: 'total_margin',
      type: 'decimalColumn'
    },
    {
      headerName: 'Total Margin %',
      field: 'total_margin_percentage',
      type: 'textColumn',
      cellStyle: { textAlign: 'right' }
    },
    {
      headerName: 'Unit Item Point',
      field: 'item_point_unit',
      type: 'decimalColumn'
    },
    {
      headerName: 'Total Item Point',
      field: 'line_point_amount',
      type: 'decimalColumn'
    },
    {
      headerName: 'Receipt',
      field: 'rct',
      type: 'textColumn',
    },
    {
      headerName: 'Rct Mode',
      field: 'rct_mode',
      type: 'textColumn'
    },
    {
      headerName: 'Invoice Bal',
      field: 'arap_bal',
      type: 'decimalColumn'
    },
    {
      headerName: 'Aging',
      field: 'aging',
      type: 'integerColumn'
    }
  ];

  readPermissionDefintion = {
    branch: 'API_TNT_DM_ERP_SALES_ITEM_COLLECTION_INVOICE_READ'
  }

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected apiService: ApiService,
    protected toastr: ToastrService,
    protected readonly componentStore: ComponentStore<LocalState>,
    protected readonly permissionStore: Store<PermissionStates>,
    protected readonly sessionStore: Store<SessionStates>) {
    super();
  }

  ngOnInit() {
    // Initialize branch GUIDs based on permissions
    this.branchGuids = UtilitiesModule.getTargetsByPermission(this.userPermissionTarget$,
      this.readPermissionDefintion.branch, 'bl_fi_mst_branch'
    );
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.viewColFacade.rowDataSalesItemCollectionInvoice$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.viewColFacade.totalRecordsSalesItemCollectionInvoice$.subscribe(totalRecords => this.totalRecords = totalRecords);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onNext() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false
    });
    this.viewColFacade.onNextAndReset(this.index, 1);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
    else
    {
      // No initial data to load
    }

    const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
    statusBarComponent.setTitle(this.compName);
    statusBarComponent.setFilename(this.compName);
    statusBarComponent.setHideGroupColumn(true);
  }

  createData() {
    this.clear();
    const formData = this.getInputModel();
    this.subs.sink = this.apiService.getDynamicReport(formData, this.apiVisa).pipe(
      mergeMap(a =>
        from(a.data).pipe(
          map(b => {
            const total_margin = b.line_amount_txn - (Math.abs(b.line_cost_ma_price_company) * b.line_quantity_base);
            Object.assign(b, {
              inv: DocumentShortCodesClass.serverDocTypeToShortCodeMapper(b.server_doc_type) + b.server_doc_1,
              unit_price_net: UtilitiesModule.divide(b.line_amount_txn, b.line_quantity_base),
              net_price_exclude_tax: b.line_amount_txn - b.line_amount_tax_gst,
              unit_cost: b.line_cost_ma_price_company,
              total_cost: b.line_cost_ma_price_company * b.line_quantity_base,
              total_margin: total_margin,
              total_margin_percentage: UtilitiesModule.divide(total_margin, b.line_amount_txn).toFixed(2) + '%',
              aging: UtilitiesModule.getDayCount(new Date(), b.date_txn),
              so: b.so_server_doc_type?DocumentShortCodesClass.serverDocTypeToShortCodeMapper(b.so_server_doc_type) + b.so_server_doc_1:'',
              sr: b.sr_server_doc_type?DocumentShortCodesClass.serverDocTypeToShortCodeMapper(b.sr_server_doc_type) + b.sr_server_doc_1:'',
              rct: this.getRCT(b.bl_fi_generic_doc_arap_contra),
              item_point_unit: UtilitiesModule.divide(b.line_point_amount, b.line_quantity_base)           
            });
            return b;
          }),
          toArray(),
          map(c => {
            a.data = c;
            return a;
          })
        )
      )
    ).subscribe(resolved => {
      console.log(resolved);
      this.totalRecords = resolved.data.length;
      this.rowData = this.transformedData(resolved.data);
      this.gridApi.setRowData(this.rowData);
      this.viewColFacade.selectRowDataSalesItemCollectionInvoice(this.rowData);
      this.viewColFacade.selectTotalRecordsSalesItemCollectionInvoice(this.totalRecords);
    }, err => {
      console.error(err);
      let msg = err.status===403 ? ErrorMessages.CRUD_403_ERRORS.READ : err.message;
      this.toastr.error(
        msg,
        "AG Gird Error",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      this.viewColFacade.loadFailed(err);
    });
  };

  getInputModel() {
    const inputModel = {} as ListingInputModel;
    inputModel.tableName = 'bl_fi_generic_doc_hdr';
    inputModel.search = (this.searchQuery?.keyword);
    inputModel.searchColumns = ['hdr.server_doc_1', 'line.item_code'];
    inputModel.status = [];
    inputModel.orderBy = 'date_txn';
    inputModel.order = '';
    inputModel.limit = null;
    inputModel.offset = 0;
    inputModel.calcTotalRecords = false;
    inputModel.showCreatedBy = false;
    inputModel.showUpdatedBy = false;
    inputModel.filterLogical= 'AND';
    inputModel.filterConditions = [
      {
        "filterColumn": "hdr.server_doc_type",
        "filterValues": ["INTERNAL_SALES_INVOICE", "INTERNAL_SALES_CASHBILL"],
        "filterOperator": "IN"
      },
      {
        "filterColumn": "hdr.posting_status",
        "filterValues": ["FINAL"],
        "filterOperator": "IN"
      }
    ];

    inputModel.joins = [
      {
        "tableName": "bl_fi_generic_doc_line",
        "joinColumn": "line.generic_doc_hdr_guid = hdr.guid",
        "alias": "line",
        "columns": ['item_guid', 'generic_doc_hdr_guid', 'item_code', 'item_name', 'item_txn_type', 'txn_type', 'quantity_base', 'unit_price_net', 'amount_txn', 'amount_tax_gst', 'cost_ma_price_company','point_amount'],
        "filterLogical": 'AND',
        "joinType": "inner join"
      },
      {
        "tableName": "bl_fi_generic_doc_link",
        "joinColumn": "so_link.guid_doc_2_line = line.guid",
        "columns": ['guid_doc_2_line','guid_doc_1_hdr'],
        "filterLogical": 'AND',
        "alias": "so_link",
        "joinType": "left join",
        "filterConditions" : [
          {
            "filterColumn": "server_doc_type_doc_1_line",
            "filterValues": ["INTERNAL_SALES_ORDER"],
            "filterOperator": "IN"
          }
        ]
      },
      {
        "tableName": "bl_fi_generic_doc_hdr",
        "joinColumn": "so.guid = so_link.guid_doc_1_hdr",
        "columns": ['server_doc_1','server_doc_type'],
        "filterLogical": 'AND',
        "alias": 'so', 
        "joinType": "left join",
      }, 
      {
        "tableName": "bl_fi_generic_doc_link",
        "joinColumn": "sr_link.guid_doc_1_line = line.guid",
        "columns": ['guid_doc_1_line','guid_doc_2_hdr'],
        "filterLogical": 'AND',
        "alias": "sr_link",
        "joinType": "left join",
        "filterConditions" : [
          {
            "filterColumn": "server_doc_type_doc_2_line",
            "filterValues": ["INTERNAL_SALES_RETURN"],
            "filterOperator": "IN"
          }
        ]
      },
      {
        "tableName": "bl_fi_generic_doc_hdr",
        "joinColumn": "sr.guid = sr_link.guid_doc_2_hdr",
        "columns": ['server_doc_1','server_doc_type'],
        "filterLogical": 'AND',
        "alias": 'sr', 
        "joinType": "left join",
      } 
    ];

    inputModel.childs = [
      {
        "tableName": "bl_fi_generic_doc_arap_contra",
        "joinColumn": "guid_doc_1_hdr",
        "filterLogical": 'AND',
        "joinType": "left join",
        "filterConditions" : [
          {
            "filterColumn": "server_doc_type_doc_2",
            "filterValues": ["INTERNAL_RECEIPT_VOUCHER"],
            "filterOperator": "IN"
          }
        ],
        "joins" : [
          {
            "tableName": "bl_fi_generic_doc_hdr",
            "joinColumn": "guid_doc_2_hdr",
            "columns": ['server_doc_1','server_doc_type'],
            "filterLogical": 'AND',
            "joinType": "left join"
          },
          {
            "tableName": "bl_fi_generic_doc_line",
            "joinColumn": "bl_fi_generic_doc_hdr.guid=bl_fi_generic_doc_line.generic_doc_hdr_guid",
            "columns": ['generic_doc_hdr_guid','item_guid','item_code','txn_type','amount_txn'],
            "filterLogical": 'AND',
            "joinType": "left join",
            "childs" : [
            {
              "tableName": "bl_fi_mst_item_ext",
              "joinColumn": "item_hdr_guid",
              "filterLogical": "AND",
              "joinType": "left join",
              "filterConditions": [
                {
                  "filterColumn": "param_code",
                  "filterValues": [
                      "CHARGES_MODE",
                      "CHARGES_RATE",
                      "CHARGES_ABSOLUTE_VALUE",
                      "CHARGES_ADDITIONAL_SURCHARGE"
                  ],
                  "filterOperator": "IN"
                }
              ],
              "joinColumnWith": "bl_fi_generic_doc_line.item_guid"
            }]
          }
        ]
      }, 
      {
        "tableName": "bl_fi_mst_item_ext",
        "joinColumn": "item_hdr_guid",
        "filterLogical": 'AND',
        "joinType": "left join",
        "filterConditions": [
          {
            "filterColumn": "param_code",
            "filterValues": ["CHARGES_MODE","CHARGES_RATE","CHARGES_ABSOLUTE_VALUE","CHARGES_ADDITIONAL_SURCHARGE"],
            "filterOperator": "IN"
          }
        ],
        "joinColumnWith" : "line.item_guid"
      }
    ]

    let filterBranch;
    if (this.branchGuids?.length > 0) {
      filterBranch = {
        "filterColumn": "hdr.guid_branch",
        "filterValues": this.branchGuids,
        "filterOperator": "IN"
      }
    }

    let filter = [];
    if (this.searchQuery?.queryString) {
      inputModel.filterDate = {
        dateFrom: UtilitiesModule.checkNull(this.searchQuery.queryString['date']['from'], null),
        dateTo: UtilitiesModule.checkNull(this.searchQuery.queryString['date']['to'], null),
        column: 'date_txn'
      }
      
      const branches = UtilitiesModule.checkNull(this.searchQuery.queryString['branch'], []);
      if (branches && branches.length > 0) {
        filterBranch = {
          "filterColumn": "hdr.guid_branch",
          "filterValues": branches,
          "filterOperator": "IN"
        }
      }
      const itemCode = UtilitiesModule.checkNull(this.searchQuery.queryString['itemCode'], '');
      if (itemCode) {
        inputModel.search = itemCode;
        inputModel.searchColumns = ['line.item_code'];
      }

      const itemTypes = UtilitiesModule.checkNull(this.searchQuery.queryString['itemType'], []);
      if (itemTypes && itemTypes.length > 0) {
        inputModel.filterConditions.push(
          {
            "filterColumn": "item_txn_type",
            "filterValues": itemTypes,
            "filterOperator": "IN"
          },
        );
      } 
      
    }
 
    if (filterBranch) {
      inputModel.filterConditions.push(filterBranch);
    }
    return inputModel;
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onSearch(e: SearchQueryModel) {
    console.log("search", e);
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3)  {
        this.toastr.error(
          'Search keyword must more than 2 characters.',
          'Keyword',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      } 
      
      this.searchQuery = e;
      this.clear();
      this.createData();

      // Set branch names asynchronously if query has date range
      if (e.queryString && e.queryString['date']) {
        const date_from = UtilitiesModule.dateFormatter(e.queryString['date']['from']);
        const date_to = UtilitiesModule.dateFormatter(e.queryString['date']['to']);
        const branchGuids = UtilitiesModule.checkNull(e.queryString['branch'], this.branchGuids);
        this.setBranchSubTitle(branchGuids, date_from, date_to);
      }
    } else {
      this.searchQuery = null;
    }
  }

  clear() {
    this.gridApi.setRowData([]);
    this.totalRecords = 0;
    this.rowData = [];
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  setDataRowCache() {
    console.log('set data row cache');
    this.gridApi.setRowData(this.rowData);
  }

  getRCT(data) {
    return data
      .map(item => DocumentShortCodesClass.serverDocTypeToShortCodeMapper(item.bl_fi_generic_doc_hdr_server_doc_type) + item.bl_fi_generic_doc_hdr_server_doc_1)
      .filter(Boolean) // optional: removes null or undefined
      .join(', ');
  }
  
  transformedData(data) {
    const transformedData = [];
  
    // ✅ Group once by guid
    const grouped = data.reduce((acc, item) => {
      if (!acc[item.guid]) acc[item.guid] = [];
      acc[item.guid].push(item);
      return acc;
    }, {});
  
    // ✅ Process each group
    for (const guid in grouped) {
      const records = grouped[guid]; // ✅ now 'records' is known to be an array
      
      const stlItems = records.filter(item => item.line_txn_type === "STL_MTHD");
      const pnsItems = records.filter(item => item.line_txn_type === "PNS");
    
      const chargesAmt = stlItems.reduce((sum, item) => {
        return sum + this.getAmountCharges(item.bl_fi_mst_item_ext, item.line_amount_txn);
      }, 0);

      const totalAmount = pnsItems.reduce((sum, item) => sum + item.line_amount_txn, 0);
      const stlItemCodes = stlItems.map(item => {
        const rate = this.getExtParamValueNumeric(item.bl_fi_mst_item_ext, 'CHARGES_RATE');
        const rateStr = (rate * 100).toFixed(2) + "%";
        return `${item.line_item_code} (${rateStr})`;
      }).join(", ");

      // rct charges
      const stlItemsRct = records.flatMap(r =>
        r.bl_fi_generic_doc_arap_contra.filter(item =>
          item.bl_fi_generic_doc_line_txn_type === "STL_MTHD"
        )
      );

      const stlItemCodesRct = stlItemsRct.map(item => {
        const rate = this.getExtParamValueNumeric(item.bl_fi_mst_item_ext, 'CHARGES_RATE');
        const rateStr = (rate * 100).toFixed(2) + "%";
        return `${item.bl_fi_generic_doc_line_item_code} (${rateStr})`;
      }).join(", ");

      const chargesAmtRct = stlItemsRct.reduce((sum, item) => {
        return sum + this.getAmountCharges(item.bl_fi_mst_item_ext, item.bl_fi_generic_doc_line_amount_txn)
      }, 0)

      const finalChargesAmt = chargesAmtRct + chargesAmt;
      const updated = pnsItems.map(item => {
        const finCost = ((finalChargesAmt) * (item.line_amount_txn / totalAmount));
        const itemUpdate =
        {
          ...item,
          fin_cost: finCost,
          fin_rate: (UtilitiesModule.divide(finCost, item.line_amount_txn)*100).toFixed(2) + '%',
          //rct_rate: (chargesRate * 100).toFixed(2) + '%',
          rct_mode: [stlItemCodes, stlItemCodesRct].filter(Boolean).join(','),
        }
        return itemUpdate; 
      });
      transformedData.push(...updated);
    }
    return transformedData;
  }

  getAmountCharges(dataExt: any[], amountTxn: number): number {
    const mode = this.getExtParamValueString(dataExt, 'CHARGES_MODE');
    const rate = this.getExtParamValueNumeric(dataExt, 'CHARGES_RATE');
    const absValue = this.getExtParamValueNumeric(dataExt, 'CHARGES_ABSOLUTE_VALUE');
    const surcharge = this.getExtParamValueNumeric(dataExt, 'CHARGES_ADDITIONAL_SURCHARGE');
  
    let result = 0;
  
    if (mode === 'RATIO' && rate !== 0) {
      result = amountTxn * rate + surcharge;
    } else if (mode === 'ABSOLUTE' && absValue !== 0) {
      result = absValue;
    }
  
    return result;
  }


  
  getExtParamValueNumeric(dataExt: any[], paramCode: string): number {
    const found = dataExt.find(e => e.param_code === paramCode);
    return found?.value_numeric ?? 0;
  }
  
  getExtParamValueString(dataExt: any[], paramCode: string): string {
    const found = dataExt.find(e => e.param_code === paramCode);
    return found?.value_string ?? '';
  }

  setBranchSubTitle(guids: string[], dateFrom: string, dateTo: string) {
    const key = 'branch';

    this.sessionStore.select(SessionSelectors.selectDropDownRowData).pipe(take(1)).subscribe(resolved => {
      let rowData = resolved ? resolved[key] ? resolved[key] : [] : [];
      let branchText = "ALL BRANCHES";

      if (rowData.length > 0 && guids && guids.length > 0) {
        const branches = rowData.filter(row => guids.includes(row.guid));
        if (branches.length > 0) {
          branchText = branches.map(branch => `${branch.name} (${branch.code})`).join(', ');
        }
      }

      this.subTitle = branchText;
      this.subTitle += "\n";
      this.subTitle += `${dateFrom} to ${dateTo}`;

      const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
      statusBarComponent.setSubTitle(this.subTitle);
    });
  }

}