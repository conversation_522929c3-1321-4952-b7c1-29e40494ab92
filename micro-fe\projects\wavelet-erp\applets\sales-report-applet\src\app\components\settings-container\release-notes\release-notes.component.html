<mat-card>
    <mat-card-content>
        <h3>Version 2.5.0 (2025-07-11)</h3>
        <ul>
            <li><span class="bold-text">Enhanced Period Selector for Daily/Weekly/Monthly Reports</span> - Sales Report by Daily Weekly Monthly now features advanced period selection with dynamic column generation</li>
            <li><span class="bold-text">Grouped Column Headers</span> - Period columns now display as grouped headers (e.g., "July 2025") with sub-columns for Qty | Amount | GP</li>
            <li><span class="bold-text">Smart Date Range Calculation</span> - Selecting a period (day/week/month) automatically calculates appropriate date ranges and generates corresponding columns</li>
            <li><span class="bold-text">Period-Based Data Aggregation</span> - Data is intelligently grouped by item and aggregated by period with proper totals calculation</li>
            <li><span class="bold-text">Improved Error Handling</span> - Enhanced error handling across all sales report components with proper 403 permission error messages</li>
            <li><span class="bold-text">Dynamic Column Generation</span> - Columns are dynamically generated based on selected period type and date range for flexible reporting</li>
        </ul>
        <h3>Version 2.4.0 (2025-07-07)</h3>
        <ul>
            <li>Add group by salesman functionality to Sales Report by Salesman for enhanced data organization</li>
        </ul>
        <h3>Version 2.3.0 (2025-07-07)</h3>
        <ul>
            <li>Add base cost filter option for enhanced cost analysis and reporting</li>
        </ul>
        <h3>Version 2.2.0 (2025-07-05)</h3>
        <ul>
            <li>Add server doctype integration to Sales Report by Document for enhanced report generation</li>
        </ul>
        <h3>Version 2.1.0 (2025-06-30)</h3>
        <ul>
            <li><span class="bold-text">Branch Permission Integration</span> - All sales reports now support branch-level permissions and filtering</li>
            <li><span class="bold-text">Dynamic Branch Display</span> - Status bar shows selected branch names with codes (e.g., "Main Branch (MB001)")</li>
            <li><span class="bold-text">Enhanced Security</span> - Users only see data from branches they have permission to access</li>
        </ul>
        <h3>Version 1.25 (2025-06-25)</h3>
        <ul>
            <li>Resolve the issue where selecting 'Replacement Cost' displays incorrect information</li>
        </ul>
        <h3>Version 1.25 (2025-06-19)</h3>
        <ul>
            <li>Add hide delta cost settings</li>
        </ul>
        <h3>Version 1.24 (2025-06-18)</h3>
        <ul>
            <li>Show delta cost of Sales Report by Document</li>
            <li>Show delta cost of Sales Report by Salesman</li>
        </ul>
        <h3>Version 1.23 (2025-05-21)</h3>
        <ul>
            <li>Apply Master Settings at AG Grid custom component</li>
        </ul>
        <h3>Version 1.22 (2025-05-20)</h3>
        <ul>
            <li>Add new Grid state drop down status bar</li>
        </ul>
        <h3>Version 1.21 (2025-05-01)</h3>
        <ul>
            <li>Show Sales Return Payment method of Daily Sales Report With Cashflow Analysis</li>
        </ul>
        <h3>Version 1.20 (2025-04-21)</h3>
        <ul>
            <li>Add new Sales Item and Collection Invoice</li>
        </ul>
        <h3>Version 1.19 (2025-03-17)</h3>
        <ul>
            <li>Add customer ref 1 and ref 2 columns at Sales Report by Document</li>
        </ul>
        <h3>Version 1.18 (2025-03-07)</h3>
        <ul>
            <li>The Daily Sales Report with Cashflow Analysis now includes additional columns: Remarks, Description, Tracking No, Client Doc, and Document Links for better tracking and reference</li>
        </ul>
        <h3>Version 1.17 (2025-02-20)</h3>
        <ul>
            <li>Resolve export issue when there are more than two group columns</li>
        </ul>
        <h3>Version 1.17 (2025-02-18)</h3>
        <ul>
            <li>Remove shadow document</li>
        </ul>
        <h3>Version 1.16 (2025-02-11)</h3>
        <ul>
            <li>Add new Report: Sales Report by Serial Number</li>
        </ul>
        <h3>Version 1.15 (2025-01-31)</h3>
        <ul>
            <li>Sales Report by Document > Error on serial number</li>
        </ul>
        <h3>Version 1.14 (2025-01-10)</h3>
        <ul>
            <li>Sales Report by item code - add optional HIDE_GL_CODE_TYPE</li>
        </ul>
        <h3>Version 1.13 (2024-11-05)</h3>
        <ul>
            <li>Add select all checkbox of item type at advance search</li>
            <li>Resolve issue for wrong data row cache of Sales Report By Salesman and Daily Gross Profit By Salesman</li>
            <li>Add auto all size column for all reports</li>
        </ul>
        <h3>Version 1.12 (2024-10-16)</h3>
        <ul>
            <li>Hourly sales report - add total row at the bottom</li>
        </ul>
        <h3>Version 1.11 (2024-10-03)</h3>
        <ul>
            <li>Hourly sales report - resolved that hour columns missing</li>
        </ul>
        <h3>Version 1.10 (2023-12-22)</h3>
        <ul>
            <li>Change Document type to Document Short Code</li>
            <li>Change Payamnet Voucher amount to negative at Daily Sales Report With Cashflow Analysi</li>
        </ul>
        <h3>Version 1.09 (2023-11-21)</h3>
        <ul>
            <li>Add Payment Voucher and Receipt at Daily Sales Report With Cashflow Analysis</li>
        </ul>
        <h3>Version 1.08 (2023-08-18)</h3>
        <ul>
            <li>Add date from and date to at export PDF</li>
            <li>Remove pagination</li>
        </ul>
        <h3>Version 1.07 (2023-08-04)</h3>
        <ul>
            <li>Add Unit Price and Unit Cost at Sales Report By Document</li>
            <li>Arrange columns of Sales Report By Document</li>
        </ul>
        <h3>Version 1.06 (2023-07-20)</h3>
        <ul>
            <li>Add Customer and Salesman at Sales Report By Document</li>
        </ul>
        <h3>Version 1.05 (2023-06-28)</h3>
        <ul>
            <li>Add Date Txn at Sales Report By Document</li>
            <li>Move export button at Top</li>
            <li>Resolved that cannot filter date</li>
        </ul>
        <h3>Version 1.04 (2023-06-10)</h3>
        <ul>
            <li>Change pagination</li>
            <li>Add export button</li>
            <li>Add new filter</li>
        </ul>
        <h3>Version 1.03 (2023-05-03)</h3>
        <ul>
            <li>Resolved that cannot export to csv</li>
        </ul>
        <h3>Version 1.02 (2023-04-19)</h3>
        <ul>
            <li>Resolved that wrong landed cost</li>
        </ul>
        <h3>Version 1.01 (2023-03-31)</h3>
        <ul>
            <li>Add Application Settings</li>
            <li>Hide Cost</li>
        </ul>
        <h3>Version 1.00 (2022-11-29)</h3>
    </mat-card-content>
</mat-card>