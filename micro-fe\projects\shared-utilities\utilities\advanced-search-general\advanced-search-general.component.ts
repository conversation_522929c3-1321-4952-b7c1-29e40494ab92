import { Component, Input, OnInit, Output, ViewEncapsulation, EventEmitter, Renderer2,  ElementRef, ViewChild, AfterViewInit, AfterViewChecked, ChangeDetectorRef, HostListener, TemplateRef } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import * as $ from 'jquery';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { SearchModelV2 } from 'projects/shared-utilities/models/search-model-v2';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { MatDialog } from '@angular/material/dialog';
import * as moment from 'moment';

@Component({
  selector: 'app-advanced-search-general',
  templateUrl: './advanced-search-general.component.html',
  styleUrls: ['./advanced-search-general.component.scss'],
  encapsulation: ViewEncapsulation.None
})

export class AdvancedSearchGeneralComponent implements AfterViewInit  {
  @ViewChild('basic', { static: true }) basicInput: ElementRef;
  @ViewChild('addDialog') addDialogTemplate!: TemplateRef<any>;
  @ViewChild('deleteDialog') deleteDialogTemplate!: TemplateRef<any>;

  private _container: ElementRef;
  private searchDebounce = false;

  @ViewChild('container', { static: false }) set containerRef(value: ElementRef) {
    if (value) {
      this._container = value;
      this.setContainerWidth();
    }
  }

  @Input() advSearchModel: SearchModelV2;
  @Input() id: string;
  @Input() showAdv: boolean;
  @Input() readPermissionDefintion;
  @Input() appletSettings?: any; // need remove

  @Output() search = new EventEmitter<SearchQueryModel>();

  isAdvanced: boolean;
  inputArray: any[];
  advForm: FormGroup;
  optionArray = {};
  filter = '';
  apiVisa = AppConfig.apiVisa;
  basicInputWidth = 0;
  isChecked = false;
  isIndeterminate = false;

  companyGuidList: any[];
  minToDateMap: { [key: string]: Date } = {};

  labels: string[] = [];
  selectedLabel: string | null = null;
  newLabel: string = '';
  advanceSearchStateSettings;

  private addDialogRef: any;
  private deleteDialogRef: any;
  private preventClose: boolean = false;

  constructor(private formBuilder: FormBuilder,
    private readonly sessionStore: Store<SessionStates>,
    private cdr: ChangeDetectorRef,
    private dialog: MatDialog,
    private renderer: Renderer2) {
    this.isAdvanced = false;
    if (!this.showAdv) {
      this.showAdv = true;
    }
  }

  @HostListener('document:keydown.escape', ['$event'])
  onEscPressed(event: KeyboardEvent): void {
    this.isAdvanced = false;
  }

  ngAfterViewInit() {
    if (this._container) {
      this.setContainerWidth();
    }
  }

  setContainerWidth() {
    if (this._container && this._container.nativeElement) {
      this.basicInputWidth = this.basicInput.nativeElement.offsetWidth + 65;
      this.renderer.setStyle(this._container.nativeElement, 'width', `${this.basicInputWidth}px`);
    } else {
      console.error('Container element or basicInput is not defined.');
    }
  }


  ngOnInit() {
    if (!this.advSearchModel) {
      this.advSearchModel = {
        label: {keyword:'keyword'},
        dataType: {keyword:'string'},
        form: new FormGroup({
          keyword: new FormControl()
          }),
        options: {keyword:{}}
      }
    }

    this.sessionStore.select(SessionSelectors.selectMasterSettings).subscribe(setting => {
      if (!setting) {
        // Fallback logic: just assign the default form and options
        this.advForm = this.advSearchModel.form;
        this.inputArray = Object.entries(this.advSearchModel.dataType);
        this.extractOptions();
        this.addCheckbox();
        this.initializeDefaultPeriods();
        return; // stop here if no settings
      }

      const keysToDelete = Object.entries(this.advSearchModel?.dataType || {})
        .filter(([key, value]) => {
          return (
            (value === 'select-multi-gl-dimension' && !setting.SHOW_GL_DIMENSION) ||
            (value === 'select-multi-segment' && !setting.SHOW_SEGMENT) ||
            (value === 'select-multi-profit-center' && !setting.SHOW_PROFIT_CENTER) ||
            (value === 'select-multi-project' && !setting.SHOW_PROJECT) ||
            (value === 'select-multi-company' && setting.HIDE_COMPANY) ||
            (value === 'select-multi-branch' && setting.HIDE_BRANCH) ||
            (key === 'transactionDate' && setting.HIDE_TRANSACTION_DATE) ||
            (key === 'createdDate' && setting.HIDE_CREATED_DATE) ||
            (key === 'updatedDate' && setting.HIDE_UPDATED_DATE)
          );
        })
        .map(([key]) => key);

      if (keysToDelete.length > 0) {
        this.advSearchModel = {
          label: this.filterObject(this.advSearchModel.label, keysToDelete),
          dataType: this.filterObject(this.advSearchModel.dataType, keysToDelete),
          form: this.advSearchModel.form,
          options: this.filterObject(this.advSearchModel.options, keysToDelete)
        };
      }

      this.advForm = this.advSearchModel.form;
      this.inputArray = Object.entries(this.advSearchModel.dataType).map((e: any) => e.flatMap(f => f));

      this.extractOptions();
      this.addCheckbox();
      this.initializeDefaultPeriods();

      for (const x of this.inputArray) {
        if(this.isCheckbox(x)){
          const checkboxName = x[0];
          const initialValue = this.advSearchModel.options[checkboxName]?.checked || false;
          const dynamicControlName = `${checkboxName}Checkbox`;
          this.advForm.patchValue({
            [dynamicControlName]: initialValue
          })
        }

      }
    });

    this.sessionStore.select(SessionSelectors.selectAdvanceSearchStateSettings).subscribe(resolve => {
      if (resolve) {
        this.advanceSearchStateSettings = resolve;
        this.labels = Object.keys(resolve[this.id] || {});
        this.labels.sort((a, b) => a.localeCompare(b));
      }
    });

    // get personal settings
    this.sessionStore.select(SessionSelectors.selectPersonalSettings).subscribe((paSettings) => {
      const key =  this.id + 'AdvancedSearch';
      if (paSettings[key]) {
        this.advForm.patchValue(paSettings[key]);

        this.inputArray.forEach(x => {
          if (x[1] === 'date') {
            const groupName = x[0];
            const group = this.advForm.get(groupName) as FormGroup;
            if (group) {
              const period = group.get('period')?.value;
              this.selectedPeriodMap[groupName] = period;
            }
          }
        });
      }

      const companiesRaw = this.advForm.get('company')?.value;
      const companies = Array.isArray(companiesRaw)
        ? companiesRaw
        : companiesRaw ? [companiesRaw] : [];
      this.companyGuidList = companies.map(c => c);
    });

    $(document).mouseup( ev => {
      const container = $(`#${this.id}`);
      const calendar = $('mat-calendar');
      const matOption = $('mat-option');
      const backdrop = $('.cdk-overlay-backdrop');

      if (!this.preventClose
          && !matOption.is(ev.target) && !calendar.is(ev.target) && !container.is(ev.target) && !backdrop.is(ev.target)
          && matOption.has(ev.target).length === 0
          && container.has(ev.target).length === 0
          && calendar.has(ev.target).length === 0
          && backdrop.has(ev.target).length === 0
         ) {
        container.hide('fast' , 'swing', c => {
          this.isAdvanced = false;
          // this.advForm.reset();
        });
      }
    });
  }

  onFromDateChange(fieldKey: string): void {
    const parentGroup = this.advForm.get(fieldKey) as FormGroup;

    if (!parentGroup) return;

    const fromControl = parentGroup.get('from');
    const toControl = parentGroup.get('to');

    const fromDate = fromControl?.value;

    if (fromDate) {
      this.minToDateMap[fieldKey] = fromDate;

      const toDate = toControl?.value;

      if (!toDate || new Date(toDate) < new Date(fromDate)) {
        toControl?.setValue(fromDate);
      }
    }
  }

  // Helper function to filter out keys from an object
  filterObject(object: any, keysToDelete: string[]): any {
    return Object.fromEntries(Object.entries(object).filter(([key]) => !keysToDelete.includes(key)));
  }

  extractOptions = (fieldName?, fieldValue?) => this.inputArray
    .filter(x => x[1] === 'select')
    .filter(x => fieldName ? x[0] === fieldName : true)
    .forEach(x => {
      const options = x[2]
        .flatMap(e => e)
        .filter(e => fieldValue ? e.toLowerCase().includes(fieldValue.toLowerCase()) : true);
      this.optionArray[x[0]] = options;
    })

  searchToggle() {
    if (this.showAdv) {
      const container = $(`#${this.id}`);
      if (!this.isAdvanced) {
        this.isAdvanced = true;
        container.show('fast', 'swing', c => this.isAdvanced = true);
      }
    }
  }

  reset() {
    this.advForm.reset();
  }

  optionSearchFilter(query) {
    const fieldName = query.target.parentNode.parentNode.id;
    this.extractOptions(fieldName, query.target.value);
  }

  basicSearch(keyword: string) {
    if (this.searchDebounce) return;

    this.searchDebounce = true;

    const container = $(`#${this.id}`);
    const query = {
      queryString: "",
      keyword: keyword,
      isEmpty: !keyword
    };
    this.search.emit(query);
    container.hide('fast' , 'swing', c => {
      this.isAdvanced = false;
      // this.advForm.reset();
    });
    setTimeout(() => this.searchDebounce = false, 500); // Debounce duration
  }

  advancedSearch() {
    if (this.searchDebounce) return;

    this.searchDebounce = true;

    const container = $(`#${this.id}`);
    const query = {
      queryString: this.advForm.value,
      keyword: "",
      isEmpty: false
    };

    const key =  this.id + 'AdvancedSearch';
    const settings = {
      settings: {
        [key]: this.advForm.value
      }
    }

    this.sessionStore.dispatch(
      SessionActions.savePersonalSettingsInitNoPopup(settings)
    );

    this.search.emit(query);
    container.hide('fast' , 'swing', c => {
      this.isAdvanced = false;
      // this.advForm.reset();
    });
    setTimeout(() => this.searchDebounce = false, 500); // Debounce duration
  }

  isRequired(x) {
    if (!this.advSearchModel || !this.advSearchModel.options || !this.advSearchModel.options[x[0]]) {
      return false;
    }
    let value = this.advSearchModel.options[x[0]]['required'];
    return this.isTrue(value);
  }

  isMultiple(x) {
    if (!this.advSearchModel || !this.advSearchModel.options || !this.advSearchModel.options[x[0]]) {
      return false;
    }
    let value = this.advSearchModel.options[x[0]]['multiple'];
    return this.isTrue(value);
  }

  isCheckbox(x) {
    if (!this.advSearchModel || !this.advSearchModel.options || !this.advSearchModel.options[x[0]]) {
      return false;
    }
    let value = this.advSearchModel.options[x[0]]['checkbox'];
    return this.isTrue(value);
  }

  isTrue(value) {
    if (value === true || value === false) {
      return value;
    }
    else if (value) {
      return value.includes(true);
    }
    return false;
  }

  addCheckbox() {
    this.inputArray.forEach(x => {
      if (this.isCheckbox(x)) {
        const checkbox = this.formBuilder.control(false);
        const name = x[0] + 'Checkbox';
        this.advForm.addControl(name, checkbox);
      }
    })
  }

  customWidth(x) {
    let customWidth = ((this.basicInputWidth-56) / 2);
    if (this.isCheckbox(x)) {
      customWidth += -15;
    }
    return customWidth + 'px';
  }

  checkForScrollBar() {
    const content = this._container.nativeElement;
    const hasScrollBar = content.scrollHeight > content.clientHeight;
  }

  onCompanySelected(e: any[]) {
    if (e && e.length > 0) {
      this.companyGuidList = e.map(company => company.guid);
    } else {
      this.companyGuidList = [];
    }
  }

  onDateOptionChange(event: any): void {
    const selectedValue = event.value;
  }

  toggleSelectAll(isChecked: boolean, optionKey: string) {
    this.isChecked = isChecked;
    this.isIndeterminate = false;

    if (isChecked) {
      const selectedItems = this.optionArray[optionKey] || [];
      this.advForm.controls[optionKey].patchValue(selectedItems);
    } else {
      this.advForm.controls[optionKey].patchValue([]);
    }
  }

  openAddDialog() {
    this.preventClose = true;
    this.newLabel = '';
    this.addDialogRef = this.dialog.open(this.addDialogTemplate);
    this.addDialogRef.afterClosed().subscribe(() => {
      this.addDialogRef = null;
      this.preventClose = false;
    });
  }

  closeAddDialog() {
    this.addDialogRef.close();
    this.addDialogRef = null;
  }

  confirmAdd() {
    const trimmed = this.newLabel.trim();
    if (trimmed) {
      this.saveAdvanceSearchState(trimmed);

      this.labels = [trimmed, ...this.labels];
      this.labels.sort((a, b) => a.localeCompare(b));
      this.selectedLabel = trimmed;
      this.cdr.markForCheck();
    }
    this.addDialogRef.close();
    this.addDialogRef = null;
  }

  openDeleteDialog() {
    this.preventClose = true;
    this.deleteDialogRef = this.dialog.open(this.deleteDialogTemplate);
    this.deleteDialogRef.afterClosed().subscribe(() => {
      this.deleteDialogRef = null;
      this.preventClose = false;
    });
  }

  closeDeleteDialog(confirmed: boolean) {
    if (confirmed && this.selectedLabel) {
      this.removeAdvanceSearchState(this.selectedLabel);
      this.labels = this.labels.filter(label => label !== this.selectedLabel);
      this.selectedLabel = null;
      this.cdr.markForCheck();
    }
    this.deleteDialogRef.close();
    this.deleteDialogRef = null;
  }

  saveAdvanceSearchState(newState) {
    this.sessionStore.dispatch(SessionActions.saveAdvanceSearchStateSettingsInit({
      settings: this.advForm.value,
      key: this.id,
      stateLabel: newState
    }));

    const key =  this.id + 'AdvancedSearch';
    const settings = {
      settings: {
        [key]: this.advForm.value
      }
    }

    this.sessionStore.dispatch(
      SessionActions.savePersonalSettingsInitNoPopup(settings)
    );
  }

  removeAdvanceSearchState(state) {
    this.sessionStore.dispatch(SessionActions.saveAdvanceSearchStateSettingsInit({
      settings: null, 
      key: this.id,
      stateLabel: state
    }));
  }

  onLabelChange(newValue) {
    const state = this.advanceSearchStateSettings[this.id][newValue];
    if (state) {
      this.advForm.patchValue(state);

      const key =  this.id + 'AdvancedSearch';
      const settings = {
        settings: {
          [key]: this.advForm.value
        }
      }

      this.sessionStore.dispatch(
        SessionActions.savePersonalSettingsInitNoPopup(settings)
      );
    }
  }

  selectedPeriodMap: { [key: string]: 'day' | 'week' | 'month' } = {};

  // Initialize default period selection for a specific field
  initializeDefaultPeriod(field: string) {
    if (!this.selectedPeriodMap[field]) {
      this.selectedPeriodMap[field] = 'day';
    }
  }

  // Initialize default periods for all date fields
  initializeDefaultPeriods() {
    if (this.inputArray) {
      this.inputArray.forEach(x => {
        const groupName = x[0];
        if (x[1] === 'date') { // Check if field type is 'date'
          this.initializeDefaultPeriod(groupName);

          const group = this.advForm.get(groupName) as FormGroup;

          // Check if group exists first
          if (group && !group.get('period')) {
            group.addControl('period', new FormControl(this.selectedPeriodMap[groupName])); // or any default
          }
        }
      });
    }
  }

  onPeriodChange(field: string) {
    const period = this.selectedPeriodMap[field];
    const parentGroup = this.advForm.get(field) as FormGroup;

    if (!parentGroup) return;

    const fromControl = parentGroup.get('from');
    const toControl = parentGroup.get('to');
    const fromDate = fromControl?.value;
    const toDate = toControl?.value;

    if (!fromDate) return;

    const fromMoment = moment(fromDate);
    const toMoment = toDate ? moment(toDate) : null;

    this.minToDateMap[field] = fromDate;

    switch (period) {
      case 'week': {
        const startOfWeek = fromMoment.clone().startOf('isoWeek');
        const endOfWeekFrom = fromMoment.clone().endOf('isoWeek').startOf('day');

        fromControl?.setValue(startOfWeek.toDate());

        if (!toMoment || toMoment.isBefore(startOfWeek)) {
          toControl?.setValue(endOfWeekFrom.toDate());
        } else {
          const endOfWeekTo = toMoment.clone().endOf('isoWeek').startOf('day');
          toControl?.setValue(endOfWeekTo.toDate());
        }

        setTimeout(() => {
          this.updateWeekInputDisplay(field, 'from', fromControl.value);
          this.updateWeekInputDisplay(field, 'to', toControl.value);
        }, 200);
        break;
      }

      case 'month': {
        const startOfMonth = fromMoment.clone().startOf('month');
        const endOfMonthFrom = fromMoment.clone().endOf('month').startOf('day');

        fromControl?.setValue(startOfMonth.toDate());

        if (!toMoment || toMoment.isBefore(startOfMonth)) {
          toControl?.setValue(endOfMonthFrom.toDate());
        } else {
          const endOfMonthTo = toMoment.clone().endOf('month').startOf('day');
          toControl?.setValue(endOfMonthTo.toDate());
        }

        break;
      }

      default:
        break;
    }
  }

  // Method to handle week picker date changes and auto-calculate range
  onWeekPickerChange(field: string) {
    this.onPeriodChange(field);
  }

  // Method to handle month picker date changes and auto-calculate range
  onMonthPickerChange(field: string) {
    this.onPeriodChange(field);
  }

  // Helper method to update individual week input display
  updateWeekInputDisplay(field: string, controlType: 'from' | 'to', dateValue: Date) {
    //console.log('updateWeekInputDisplay', field, controlType, dateValue);
    const date = moment(dateValue);
    if (!date.isValid()) return;

    const startOfWeek = date.clone().startOf('isoWeek');
    const weekNumber = startOfWeek.isoWeek();
    const isoWeekYear = startOfWeek.isoWeekYear();
    const weekFormat = `${isoWeekYear}-W${weekNumber.toString().padStart(2, '0')}`;

    // Get the actual label used by the week picker component
    const fieldLabel = this.advSearchModel?.label?.[field] || field;
    const fullLabel = `${fieldLabel} ${controlType === 'from' ? 'From' : 'To'} (Week)`;
    const inputId = this.getInputId(fullLabel, controlType);
    let inputElement = document.getElementById(inputId) as HTMLInputElement;

    // Fallback to class selector if ID not found
    if (!inputElement) {
      inputElement = document.querySelector('.week-picker-input') as HTMLInputElement;
    }

    if (inputElement) {
      //console.log('updateWeekInputDisplay', field, controlType, weekFormat);
      inputElement.value = weekFormat;
    } else {
      console.warn(`Could not find week picker input for ${field} ${controlType}`, {
        inputId,
        fullLabel,
        fieldLabel
      });
    }
  }

  getInputId(label: string, controlType: 'from' | 'to') {
    // Generate ID based on the full label passed to the week picker
    const id = `week-picker-${label.replace(/\s+/g, '-').toLowerCase()}`;
    //console.log('Generated ID for week picker:', id, 'for label:', label);
    return id;
  }
}
