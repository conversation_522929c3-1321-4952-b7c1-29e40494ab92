// Angular Core
import { ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';

// NgRx
import { Store } from "@ngrx/store";
import { ComponentStore } from '@ngrx/component-store';

// Third-party Libraries
import { GridOptions } from 'ag-grid-enterprise';
import { ErrorMessages } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { Observable, from } from 'rxjs';
import { map, mergeMap, toArray, take } from 'rxjs/operators';
import { SubSink } from 'subsink2';

// Session Controller
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';

// Permissions
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';

// Shared Utilities
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';

// Application Imports
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { SalesReportBySalesmanSearchModel } from '../../../models/advanced-search-models/sales-report-by-salesman-search.model';
import { SalesReportByItemCodeInputModel, SalesReportByItemCodeModel } from '../../../models/sales-report-by-item-code-model';
import { ApiService } from '../../../services/api-service';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-sales-report-by-salesman',
  templateUrl: './sales-report-by-salesman.component.html',
  styleUrls: ['./sales-report-by-salesman.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class SalesReportBySalesmanComponent extends ViewColumnComponent implements OnInit, OnDestroy {

  protected subs = new SubSink();

  compId = 'salesReportBySalesman';
  compName = 'Sales Report By Salesman';
  protected readonly index = 0;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  protected localState: LocalState;

  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  toggleColumn$: Observable<boolean>;
  searchModel = SalesReportBySalesmanSearchModel;

  showColumns = [
    { name: 'cost', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'delta_cost', setting: 'HIDE_DELTA_COST', permission: 'SHOW_DELTA_COST' },
  ]

  rowData = [];
  totalRecords = 0;
  salesCost = 'cost_ma';
  branchGuids = [];

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;

  gridOptions: GridOptions = {
    pagination: false,
    groupIncludeTotalFooter: true,
  };

  columnsDefs = [
    {
      headerName: 'Salesman',
      field: 'sales_entity_hdr_name',
      type: 'textColumn',
    },
    {
      headerName: 'Salesman Code',
      field: 'sales_entity_hdr_code',
      type: 'textColumn',
    },
    {
      headerName: 'Sales',
      field: 'total_sales_amount_txn',
      type: 'decimalColumn'
    },
    {
      headerName: 'Returns',
      field: 'total_return_amount_txn',
      type: 'decimalColumn'
    },
    {
      headerName: 'CM',
      field: 'total_cm_amount_txn',
      type: 'decimalColumn'
    },
    {
      headerName: 'Net Sales',
      field: 'net',
      type: 'decimalColumn'
    },
    {
      headerName: 'MA Cost',
      field: 'cost',
      type: 'decimalColumn'
    },
     {
      headerName: 'Delta Cost',
      field: 'delta_cost',
      type: 'decimalColumn'
    }
  ];

  readPermissionDefintion = {
    branch: 'API_TNT_DM_ERP_SALES_REPORT_BY_SALESMAN_READ'
  }

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected apiService: ApiService,
    protected toastr: ToastrService,
    protected readonly componentStore: ComponentStore<LocalState>,
    private readonly permissionStore: Store<PermissionStates>,
    protected readonly sessionStore: Store<SessionStates>) {
    super();
  }

  ngOnInit() {
    // Initialize branch GUIDs based on permissions
    this.branchGuids = UtilitiesModule.getTargetsByPermission(this.userPermissionTarget$,
      this.readPermissionDefintion.branch, 'bl_fi_mst_branch'
    );
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.viewColFacade.rowDataBySalesman$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.viewColFacade.totalRecordsBySalesman$.subscribe(totalRecords => this.totalRecords = totalRecords);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
    else
    {
      //this.createData();
    }

    const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
    statusBarComponent.setTitle(this.compName);
    statusBarComponent.setFilename(this.compName);
    //statusBarComponent.setHideGroupColumn(true);
  }

  createData(inputModel?: SalesReportByItemCodeInputModel) {
    this.clear();
    this.viewColFacade.loadInit(this.gridApi);
    this.subs.sink = this.apiService.getSalesReportBySalesman(inputModel, this.apiVisa).pipe(
      mergeMap(a => from(a.data).pipe(
        map(b => {
          const sales_cost = this.getSalesCost(b);
          Object.assign(b,
            {
              qty: Math.abs(b.qty_sold),
              cost: sales_cost,
              net: b.total_sales_amount_txn - b.total_return_amount_txn - b.total_cm_amount_txn,
              delta_cost: b.delta_price1_amount ?? 0
            }
          )
          return b;
        }),
        toArray(),
        map(c => {
          a.data = c;
          return a;
        })
      ))
    ).subscribe(resolved => {
      //console.log(resolved);
      this.viewColFacade.loadSuccess(resolved);
      this.totalRecords = resolved.data.length;
      this.rowData = this.groupAndSumKeepFormat(resolved.data);// [...this.rowData, ...resolved.data];
      this.gridApi.setRowData(this.rowData);
      this.viewColFacade.selectRowDataBySalesman(this.rowData);
      this.viewColFacade.selectTotalRecordsBySalesman(this.totalRecords);
    }, err => {
      console.error(err);
      let msg = err.status===403 ? ErrorMessages.CRUD_403_ERRORS.READ : err.message;
      this.toastr.error(
        msg,
        "AG Gird Error",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      this.viewColFacade.loadFailed(err);
    });
  };

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onSearch(e: SearchQueryModel) {
    console.log("search", e);
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3)  {
        this.toastr.error(
          'Search keyword must more than 2 characters.',
          'Keyword',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      }

      const inputModel = {} as SalesReportByItemCodeInputModel;
      inputModel.keyword = e.keyword;
      inputModel.date_from = '2022-01-01T00:00:00.000Z';
      inputModel.date_to = '2099-12-31T00:00:00.000Z';
      inputModel.branch_guids = this.branchGuids ?? [];

      if (e.queryString) {
        inputModel.keyword = UtilitiesModule.checkNull(e.queryString['itemCode'],'');
        inputModel.branch_guids = UtilitiesModule.checkNull(e.queryString['branch'],[]);
        inputModel.date_from = UtilitiesModule.checkNull(e.queryString['date']['from'],'2022-01-01T00:00:00.000Z');
        inputModel.date_to = UtilitiesModule.checkNull(e.queryString['date']['to'],'2099-12-31T00:00:00.000Z');
        inputModel.customer_guids = UtilitiesModule.checkNull(e.queryString['customer'],[]);
        inputModel.salesman_guids = UtilitiesModule.checkNull(e.queryString['salesman'],[]);
        inputModel.item_type = UtilitiesModule.checkNull(e.queryString['itemType'],[]);
        inputModel.item_status = UtilitiesModule.checkNull(e.queryString['itemStatus'],[]);
        inputModel.item_category1_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel1'],[]);
        inputModel.item_category2_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel2'],[]);
        inputModel.item_category3_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel3'],[]);
        inputModel.item_category4_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel4'],[]);
        inputModel.item_category5_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel5'],[]);
        inputModel.item_category6_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel6'],[]);
        inputModel.item_category7_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel7'],[]);
        inputModel.item_category8_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel8'],[]);
        inputModel.item_category9_guids = UtilitiesModule.checkNull(e.queryString['itemCategoryLevel9'],[]);
        inputModel.item_category10_guids =UtilitiesModule.checkNull( e.queryString['itemCategoryLevel10'],[]);

        this.salesCost = UtilitiesModule.checkNull(e.queryString['calculateBaseOn'],'');
      }

      console.log('inputModel', inputModel);
      this.createData(inputModel);

      const date_from = UtilitiesModule.dateFormatter(inputModel.date_from);
      const date_to = UtilitiesModule.dateFormatter(inputModel.date_to);
      
      // Set branch names asynchronously
      this.setBranchSubTitle(inputModel.branch_guids, date_from, date_to);
    }
    else {
      //this.createData();
    }
  }

  clear() {
    this.gridApi.setRowData([]);
    this.totalRecords = 0;
    this.rowData = [];
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  setDataRowCache() {
    //console.log('set data row cache');
    this.gridApi.setRowData(this.rowData);
  }

  getSalesCost(model: SalesReportByItemCodeModel) {
    return model[this.salesCost + '_amount'];
  }

  calculateTotal(node, key) {
    if (node.group) {
      let totalCost = 0;
      const children = node.childrenAfterGroup;

      children.forEach((childNode) => {
        const childCost = this.calculateTotal(childNode, key);
        totalCost += childCost;
      });

      return totalCost;
    } else {
      const rowData = node.data;
      return rowData && rowData[key] ? rowData[key] : 0;
    }
  }

  setBranchSubTitle(guids: string[], dateFrom: string, dateTo: string) {
    const key = 'branch';

    this.sessionStore.select(SessionSelectors.selectDropDownRowData).pipe(take(1)).subscribe(resolved => {
      let rowData = resolved ? resolved[key] ? resolved[key] : [] : [];
      let branchText = "ALL BRANCHES";

      if (rowData.length > 0 && guids && guids.length > 0) {
        const branches = rowData.filter(row => guids.includes(row.guid));
        if (branches.length > 0) {
          branchText = branches.map(branch => `${branch.name} (${branch.code})`).join(', ');
        }
      }

      let subTitle = branchText;
      subTitle += "\n";
      subTitle += `${dateFrom} to ${dateTo}`;

      const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
      statusBarComponent.setSubTitle(subTitle);
    });
  }

  groupAndSumKeepFormat(data: any[]): any[] {
    const groupedMap = new Map<string, any>();

    for (const row of data) {
      const name = row.sales_entity_hdr_name;
      const code = row.sales_entity_hdr_code;
      const key = name; // grouping by name only

      if (!groupedMap.has(key)) {
        // Initialize with zeroed version of the row
        const newEntry: any = {};
        for (const field in row) {
          const value = row[field];
          newEntry[field] = typeof value === 'number' ? value : value ?? null;
        }
        groupedMap.set(key, newEntry);
      } else {
        const target = groupedMap.get(key);
        for (const field in row) {
          const value = row[field];
          if (typeof value === 'number') {
            target[field] = (target[field] ?? 0) + value;
          } else if (!target[field] && value != null) {
            // Set non-null text/code fields if not already set
            target[field] = value;
          }
        }
      }
    }

    return Array.from(groupedMap.values());
  }
}
