// Angular Core
import { ChangeDetectionStrategy, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';

// Angular Material
import { MatDialog } from '@angular/material/dialog';

// NgRx
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';

// Third-party Libraries
import { GridOptions } from 'ag-grid-enterprise';
import { DocumentShortCodesClass, GenericDocAllService, ErrorMessages } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { Observable, from } from 'rxjs';
import { map, mergeMap, take, toArray } from 'rxjs/operators';
import { SubSink } from 'subsink2';

// Shared Utilities
import { AkaunGenericDocViewDialogComponent } from 'projects/shared-utilities/dialogues/akaun-generic-doc-view-dialog/akaun-generic-doc-view-dialog.component';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { CellClickHandlerComponent } from 'projects/shared-utilities/utilities/cell-click-handler.component';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';

// Application Imports
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { ReceiptWithCreditCardSearchModel } from '../../../models/advanced-search-models/receipt-with-credit-card-search.model';
import { CollectionCreditCardInputDto, SalesReportByItemCodeModel } from '../../../models/sales-report-by-item-code-model';
import { ApiService } from '../../../services/api-service';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-receipt-with-credit-card',
  templateUrl: './receipt-with-credit-card.component.html',
  styleUrls: ['./receipt-with-credit-card.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class ReceiptWithCreditCardComponent extends ViewColumnComponent implements OnInit, OnDestroy {
  @ViewChild(CellClickHandlerComponent) cellClickHandler!: CellClickHandlerComponent;
  protected subs = new SubSink();

  compId = 'receiptWithCreditCard';
  compName = 'Receipt with Credit Card';
  protected readonly index = 0;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  protected localState: LocalState;

  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  toggleColumn$: Observable<boolean>;
  searchModel = ReceiptWithCreditCardSearchModel;

  showColumns = [
    { name: 'sales_cost', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
    { name: 'landed_ma_amount', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp_landed', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage_landed', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
  ]

  branchGuids = [];
  rowData = [];
  totalRecords = 0;
  salesCost = 'cost_ma';

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;

  gridOptions: GridOptions = {
    pagination: false,
    groupIncludeTotalFooter: true,
    onCellClicked: (event) => this.onCellClicked(event)
  };

  columnsDefs = [
    {
      headerName: 'Cashbook',
      field: 'cashbook_name',
      rowGroup: true,
      type: 'textColumn'
    },
    {
      headerName: 'Payment Method',
      field: 'settlement_code',
      rowGroup: true,
      type: 'textColumn',
    },
    {
      headerName: 'Branch',
      field: 'code_branch',
      type: 'textColumn',
    },
    {
      headerName: 'Doc No By Branch',
      field: 'server_doc_3',
      type: 'textColumn',
    },
    {
      headerName: 'Doc Short Code',
      field: 'docShortCode',
      type: 'textColumn',
    },
    {
      headerName: 'Doc No',
      field: 'server_doc_1',
      type: 'textColumn',
      cellStyle: { textAlign: 'left', cursor: 'pointer', color: 'blue' },
      tooltipValueGetter: () => 'Click to view',
      cellRenderer: (params) => {
        return params.value != null ? `<span class="clickable-cell">${params.value}</span>` : '';
      }
    },
    {
      headerName: 'Creation Date',
      field: 'created_date',
      type: 'dateColumn',
    },
    {
      headerName: 'Txn Date',
      field: 'date_txn',
      type: 'dateColumn',
    },
    {
      headerName: 'Payer',
      field: 'customer_name',
      type: 'textColumn',
    },
    {
      headerName: 'Credit Card No',
      field: 'cardNo',
      type: 'textColumn',
    },
    {
      headerName: 'Card Name',
      field: 'nameOnCard',
      type: 'textColumn',
    },
    {
      headerName: 'Approval Code',
      field: 'approvalCode',
      type: 'textColumn',
    },
    {
      headerName: 'Batch',
      field: 'batch',
      type: 'textColumn',
    },
    {
      headerName: 'Amount',
      field: 'amount',
      type: 'decimalColumn',
    },
    {
      headerName: 'Card Charges',
      field: 'finance_charge_amount',
      type: 'decimalColumn',
    },
    {
      headerName: 'Net Amount',
      field: 'amount_net',
      type: 'decimalColumn',
    },
    {
      headerName: 'Remarks',
      field: 'doc_remarks',
      type: 'textColumn',
    },
    {
      headerName: 'Salesman',
      field: 'sales_entity_hdr_name',
      type: 'textColumn',
    },
    {
      headerName: 'Created By',
      field: 'created_by_name',
      type: 'textColumn',
    },
    {
      headerName: 'Updated By',
      field: 'updated_by_name',
      type: 'textColumn',
    },
  ];

  readPermissionDefintion = {
    branch: 'API_TNT_DM_ERP_OFFICIAL_RECEIPT_BY_CREDIT_CARD_READ'
  }

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected apiService: ApiService,
    protected toastr: ToastrService,
    protected readonly componentStore: ComponentStore<LocalState>,
    protected dialog: MatDialog,
    private genDocService: GenericDocAllService,
    private readonly permissionStore: Store<PermissionStates>,
    protected readonly sessionStore: Store<SessionStates>) {
    super();
  }

  ngOnInit() {
    // Initialize branch GUIDs based on permissions
    this.branchGuids = UtilitiesModule.getTargetsByPermission(this.userPermissionTarget$,
      this.readPermissionDefintion.branch, 'bl_fi_mst_branch'
    );

    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.viewColFacade.rowDataByRCC$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.viewColFacade.totalRecords$.subscribe(totalRecords => this.totalRecords = totalRecords);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onNext() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false
    });
    this.viewColFacade.onNextAndReset(this.index, 1);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
    else
    {
      //this.createData();
    }

    const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
    statusBarComponent.setTitle(this.compName);
    statusBarComponent.setFilename(this.compName);
    statusBarComponent.setHideGroupColumn(true);
  }

  createData(inputModel?: CollectionCreditCardInputDto) {
    this.clear();
    this.viewColFacade.loadInit(this.gridApi);
    this.subs.sink = this.apiService.getReceiptWithCreditCard(inputModel, this.apiVisa).pipe(
      mergeMap(a => from(a.data).pipe(
        map(b => {
          const amt = b.server_doc_type === 'INTERNAL_RECEIPT_VOUCHER' ? b.amount_internal_settlement : b.amount_txn;
          Object.assign(b,
            {
              batch: b.line_property_json?.batch,
              approvalCode: b.line_property_json?.approvalCode,
              nameOnCard: b.line_property_json?.nameOnCard,
              cardNo: b.line_property_json?.cardNo,
              docShortCode: DocumentShortCodesClass.serverDocTypeToShortCodeMapper(b['server_doc_type']),
			        amount: amt,
              amount_net: amt - b.finance_charge_amount
            }
          )
          return b;
        }),
        toArray(),
        map(c => {
          a.data = c;
          return a;
        })
      ))
    ).subscribe(resolved => {
      //console.log(resolved);
      this.viewColFacade.loadSuccess(resolved);
      this.totalRecords = resolved.data.length;
      this.rowData = [...this.rowData, ...resolved.data];
      this.gridApi.setRowData(this.rowData);
      this.viewColFacade.selectRowDataByRCC(this.rowData);
      this.viewColFacade.selectTotalRecords(this.totalRecords);
    }, err => {
      console.error(err);
      let msg = err.status===403 ? ErrorMessages.CRUD_403_ERRORS.READ : err.message;
      this.toastr.error(
        msg,
        "AG Gird Error",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      this.viewColFacade.loadFailed(err);
    });
  };

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onSearch(e: SearchQueryModel) {
    //console.log("search", e);
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3)  {
        this.toastr.error(
          'Search keyword must more than 2 characters.',
          'Keyword',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      }

      const inputModel = {} as CollectionCreditCardInputDto;
      inputModel.keyword = e.keyword;
      inputModel.date_from = '2022-01-01T00:00:00.000Z';
      inputModel.date_to = '2099-12-31T00:00:00.000Z';
      inputModel.branch_guids = this.branchGuids ?? [];

      if (e.queryString) {
        inputModel.keyword = UtilitiesModule.checkNull(e.queryString['itemCode'],'');
        inputModel.branch_guids = UtilitiesModule.checkNull(e.queryString['branch'],[]);

        inputModel.salesman_guids = UtilitiesModule.checkNull(e.queryString['salesman'],[]);
        inputModel.cashbook_guids =  UtilitiesModule.checkNull(e.queryString['cashbook'],[]);
        inputModel.customer_category_guids = UtilitiesModule.checkNull(e.queryString['customerCategory'],[]);
        inputModel.credit_card_guids = UtilitiesModule.checkNull(e.queryString['creditCard'],[]);
      }
      if (e.queryString['dateTxnCheckbox'] && e.queryString['dateCreateCheckbox'])  {
        this.toastr.error(
          'Please choose either the date txn or date create filter',
          'Error',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1800
          }
        );
        return;
      }
      if(UtilitiesModule.checkNull(e.queryString['dateTxnCheckbox'],'')){
        inputModel.date_from = UtilitiesModule.checkNull(e.queryString['dateTxn']['from'],'2022-01-01T00:00:00.000Z');
        inputModel.date_to = UtilitiesModule.checkNull(e.queryString['dateTxn']['to'],'2099-12-31T00:00:00.000Z');
        inputModel.date_option ="DATE_TXN";
      }else if(UtilitiesModule.checkNull(e.queryString['dateCreateCheckbox'],'')){
        inputModel.date_from = UtilitiesModule.checkNull(e.queryString['dateCreate']['from'],'2022-01-01T00:00:00.000Z');
        inputModel.date_to = UtilitiesModule.checkNull(e.queryString['dateCreate']['to'],'2099-12-31T00:00:00.000Z');
        inputModel.date_option ="DATE_CREATE";
      }

      //console.log('inputModel', inputModel);
      this.createData(inputModel);

      const date_from = UtilitiesModule.dateFormatter(inputModel.date_from);
      const date_to = UtilitiesModule.dateFormatter(inputModel.date_to);

      // Set branch names asynchronously
      this.setBranchSubTitle(inputModel.branch_guids, date_from, date_to);
    }
    else {
      //this.createData();
    }
  }

  clear() {
    this.gridApi.setRowData([]);
    this.totalRecords = 0;
    this.rowData = [];
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  setDataRowCache() {
    //console.log('set data row cache');
    this.gridApi.setRowData(this.rowData);
  }

  onCellClicked(e: any) {
    if (e.colDef.field === "server_doc_1") {
      this.openPopup(e.data); // Pass the row data to the popup
    }
    else if (this.cellClickHandler) {
      this.cellClickHandler.onCellClicked(null,e.data.server_doc_1, e.data.server_doc_type);
    }
  }
  openPopup(documentData: any) {
    //console.log("documentData", documentData);
    let data = typeof documentData === 'string' ? JSON.parse(documentData) : documentData;
    let dtoObject = {
      "server_doc_1" : data.server_doc_1,
      "server_doc_type" : data.server_doc_type,
    }
    this.subs.sink = this.genDocService.getByGenericCriteriaSnapshot(dtoObject, AppConfig.apiVisa).subscribe(response=>{

      //console.log('document data', response.data[0]);
      this.sessionStore.dispatch(SessionActions.selectDocument({document:response.data[0]}));
      this.dialog.open(AkaunGenericDocViewDialogComponent, {
        width: '80vw',
        data: response.data[0]
      });
    })
  }

  setBranchSubTitle(guids: string[], dateFrom: string, dateTo: string) {
    const key = 'branch';

    this.sessionStore.select(SessionSelectors.selectDropDownRowData).pipe(take(1)).subscribe(resolved => {
      let rowData = resolved ? resolved[key] ? resolved[key] : [] : [];
      let branchText = "ALL BRANCHES";

      if (rowData.length > 0 && guids && guids.length > 0) {
        const branches = rowData.filter(row => guids.includes(row.guid));
        if (branches.length > 0) {
          branchText = branches.map(branch => `${branch.name} (${branch.code})`).join(', ');
        }
      }

      let subTitle = branchText;
      subTitle += "\n";
      subTitle += `${dateFrom} to ${dateTo}`;

      const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
      statusBarComponent.setSubTitle(subTitle);
    });
  }
}
