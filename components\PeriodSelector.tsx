import React, { useState } from "react";

type PeriodType = "monthly" | "quarterly" | "yearly";

function getPeriodDates(period: PeriodType, baseDate: Date) {
    const year = baseDate.getFullYear();
    const month = baseDate.getMonth();
    if (period === "monthly") {
        const from = new Date(year, month, 1);
        const to = new Date(year, month + 1, 0);
        return { from, to };
    }
    if (period === "quarterly") {
        const quarter = Math.floor(month / 3);
        const from = new Date(year, quarter * 3, 1);
        const to = new Date(year, quarter * 3 + 3, 0);
        return { from, to };
    }
    // yearly
    return { from: new Date(year, 0, 1), to: new Date(year, 11, 31) };
}

function generateColumns(period: PeriodType, from: Date, to: Date) {
    const columns: string[] = [];
    if (period === "monthly") {
        let d = new Date(from);
        while (d <= to) {
            columns.push(`${d.getFullYear()}-${d.getMonth() + 1}`);
            d.setMonth(d.getMonth() + 1);
        }
    } else if (period === "quarterly") {
        let d = new Date(from);
        while (d <= to) {
            const quarter = Math.floor(d.getMonth() / 3) + 1;
            columns.push(`${d.getFullYear()} Q${quarter}`);
            d.setMonth(d.getMonth() + 3);
        }
    } else {
        columns.push(`${from.getFullYear()}`);
    }
    return columns;
}

export default function PeriodSelector() {
    const [period, setPeriod] = useState<PeriodType>("monthly");
    const [baseDate, setBaseDate] = useState<Date>(new Date());
    const { from, to } = getPeriodDates(period, baseDate);
    const columns = generateColumns(period, from, to);

    return (
        <div>
            <label>
                Period:
                <select value={period} onChange={e => setPeriod(e.target.value as PeriodType)}>
                    <option value="monthly">Monthly</option>
                    <option value="quarterly">Quarterly</option>
                    <option value="yearly">Yearly</option>
                </select>
            </label>
            <label>
                Base Date:
                <input
                    type="date"
                    value={baseDate.toISOString().slice(0, 10)}
                    onChange={e => setBaseDate(new Date(e.target.value))}
                />
            </label>
            <div>
                Date From: {from.toISOString().slice(0, 10)} <br />
                Date To: {to.toISOString().slice(0, 10)}
            </div>
            <div>
                <strong>Columns:</strong>
                <ul>
                    {columns.map(col => (
                        <li key={col}>{col}</li>
                    ))}
                </ul>
            </div>
        </div>
    );
}
