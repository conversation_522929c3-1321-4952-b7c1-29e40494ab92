// Angular Core
import { ChangeDetectionStrategy, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';

// Angular Material
import { MatDialog } from '@angular/material/dialog';

// NgRx
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';

// Third-party Libraries
import { GridOptions } from 'ag-grid-enterprise';
import { DocumentShortCodesClass, ErrorMessages, GenericDocAllService } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { Observable, from } from 'rxjs';
import { map, mergeMap, take, toArray } from 'rxjs/operators';
import { SubSink } from 'subsink2';

// Shared Utilities - Dialogues
import { AkaunGenericDocViewDialogComponent } from 'projects/shared-utilities/dialogues/akaun-generic-doc-view-dialog/akaun-generic-doc-view-dialog.component';

// Shared Utilities - Models
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';

// Shared Utilities - Permissions
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';

// Shared Utilities - Session
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';

// Shared Utilities - Components & Utilities
import { CellClickHandlerComponent } from 'projects/shared-utilities/utilities/cell-click-handler.component';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';

// Application Imports
import { ApiService } from '../../../services/api-service';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { DailySalesReportCashflowSearchModel } from '../../../models/advanced-search-models/daily-sales-report-cashflow-search.model';
import { SalesReportByItemCodeInputModel, SalesReportByItemCodeModel } from '../../../models/sales-report-by-item-code-model';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-daily-sales-report-cashflow',
  templateUrl: './daily-sales-report-cashflow.component.html',
  styleUrls: ['./daily-sales-report-cashflow.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class DailySalesReportCashflowComponent extends ViewColumnComponent implements OnInit, OnDestroy {
  @ViewChild(CellClickHandlerComponent) cellClickHandler!: CellClickHandlerComponent;
  protected subs = new SubSink();

  compId = 'dailySalesReportCashflowAnalysis';
  compName = 'Daily Sales Report With Cashflow Analysis';
  protected readonly index = 0;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  protected localState: LocalState;

  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  toggleColumn$: Observable<boolean>;
  searchModel = DailySalesReportCashflowSearchModel;

  rowData = [];
  totalRecords = 0;
  salesCost = 'cost_ma';

  // Permission and Branch properties
  branchGuids: string[] = [];
  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  showColumns = [
    { name: 'sales_cost', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
    { name: 'landed_ma_amount', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp_landed', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage_landed', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
  ]

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;

  gridOptions: GridOptions = {
    pagination: false,
    groupIncludeTotalFooter: true,
    onCellClicked: (event) => this.onCellClicked(event)
  };

  columnsDefs = [
    {
      headerName: 'Section',
      field: 'section',
      type: 'textColumn',
      rowGroup: true, hide: true
    },
    {
      headerName: 'Branch',
      field: 'branch_name',
      type: 'textColumn'
    },
    {
      headerName: 'Branch Descr',
      field: 'branch_descr',
      type: 'textColumn'
    },
    {
      headerName: 'Branch Running No',
      field: 'hdr.server_doc_2',
      type: 'textColumn'
    },
    {
      headerName: 'Customer',
      field: 'entity_name',
      type: 'textColumn'
    },
    {
      headerName: 'Date',
      field: 'hdr.date_txn',
      type: 'dateColumn'
    },
    {
      headerName: 'Doc Short Code',
      field: 'docShortCode',
      type: 'textColumn',
    },
    {
      headerName: 'Doc No',
      field: 'hdr.server_doc_1',
      type: 'textColumn',
      cellStyle: { textAlign: 'left', cursor: 'pointer', color: 'blue' },
      tooltipValueGetter: () => 'Click to view',
      cellRenderer: (params) => {
        return params.value != null ? `<span class="clickable-cell">${params.value}</span>` : '';
      }
    },
    {
      headerName: 'Desc',
      field: 'hdr.doc_desc',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Remarks',
      field: 'hdr.doc_remarks',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Tracking',
      field: 'hdr.tracking_id',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Client Doc 1',
      field: 'hdr.client_doc_1',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Client Doc 2',
      field: 'hdr.client_doc_2',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Client Doc 3',
      field: 'hdr.client_doc_3',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Client Doc 4',
      field: 'hdr.client_doc_4',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Client Doc 5',
      field: 'hdr.client_doc_5',
      hide: true,
      type: 'textColumn'
    },
    {
      headerName: 'Doc Links',
      field: 'linkedDocuments',
      type: 'textColumn'
    },
    {
      headerName: 'Pmt Info',
      field: 'contraDocShortCodes',
      type: 'textColumn'
    },
    {
      headerName: 'Cash',
      field: 'total_rct_cash',
      type: 'decimalColumn'
    },
    {
      headerName: 'Card',
      field: 'total_rct_card',
      type: 'decimalColumn'
    },
    {
      headerName: 'Cheque',
      field: 'total_rct_cheque',
      type: 'decimalColumn'
    },
    {
      headerName: 'PD Cheque',
      field: 'total_rct_pd_cheque',
      type: 'decimalColumn'
    },
    {
      headerName: 'Coupon',
      field: 'total_rct_coupon',
      type: 'decimalColumn'
    },
    {
      headerName: 'Bank Transfer',
      field: 'total_rct_bank_transfer',
      type: 'decimalColumn'
    },
    {
      headerName: 'E Wallet',
      field: 'total_rct_e_wallet',
      type: 'decimalColumn'
    },
    {
      headerName: 'Other',
      field: 'total_rct_other',
      type: 'decimalColumn'
    },
    {
      headerName: 'Terms',
      field: 'term',
      type: 'decimalColumn'
    },
    {
      headerName: 'Document Amt',
      field: 'hdr.amount_txn',
      type: 'decimalColumn'
    },
    {
      headerName: 'Disc',
      field: 'hdr.amount_discount',
      type: 'decimalColumn'
    },
    {
      headerName: 'Tax',
      field: 'hdr.amount_tax_gst',
      type: 'decimalColumn'
    },
    {
      headerName: 'Trade In',
      field: 'total_trade_in_amount_txn',
      type: 'decimalColumn'
    },
    {
      headerName: 'User',
      field: 'hdr.created_by_name',
      type: 'textColumn'
    },
    {
      headerName: 'Salesman',
      field: 'hdr.sales_entity_hdr_name',
      type: 'textColumn'
    },
    {
      headerName: 'Salesman Code',
      field: 'hdr.sales_entity_hdr_code',
      type: 'textColumn'
    },
  ];

  readPermissionDefintion = {
    branch : 'API_TNT_DM_ERP_SALES_REPORT_CASHFLOW_ANALYSIS_READ'
  }

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected apiService: ApiService,
    protected toastr: ToastrService,
    protected readonly componentStore: ComponentStore<LocalState>,
    protected dialog: MatDialog,
    private genDocService: GenericDocAllService,
    private readonly permissionStore: Store<PermissionStates>,
    protected readonly sessionStore: Store<SessionStates>) {
    super();
  }

  ngOnInit() {
    // Initialize branch GUIDs based on permissions
    this.branchGuids = UtilitiesModule.getTargetsByPermission(this.userPermissionTarget$,
      this.readPermissionDefintion.branch, 'bl_fi_mst_branch'
    );

    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.viewColFacade.rowDataByDS$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.viewColFacade.totalRecordsByDS$.subscribe(totalRecords => this.totalRecords = totalRecords);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onNext() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false
    });
    this.viewColFacade.onNextAndReset(this.index, 1);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
    statusBarComponent.setTitle(this.compName);
    statusBarComponent.setFilename(this.compName);
    statusBarComponent.setHideGroupColumn(true);

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
    else
    {
      //this.createData();
    }
  }

  createData(inputModel?: SalesReportByItemCodeInputModel) {
    console.log("on create data....");
    this.clear();
    this.viewColFacade.loadInit(this.gridApi);
    this.subs.sink = this.apiService.getDailySalesReportCashflow(inputModel, this.apiVisa).pipe(
    ).subscribe(resolved => {
      console.log(resolved);
      let reportList = [];

      const salesWithSection = resolved.data[0].sales.map(sale => ({
        ...sale,
        section: 'Sales and Collection'
      }));

      const salesReturnWithSection = resolved.data[0].salesReturn.map(salesReturn => ({
        ...salesReturn,
        total_rct_cash: salesReturn.total_rct_cash * salesReturn.hdr.amount_signum,
        total_rct_card: salesReturn.total_rct_card * salesReturn.hdr.amount_signum,
        total_rct_cheque: salesReturn.total_rct_cheque * salesReturn.hdr.amount_signum,
        total_rct_pd_cheque: salesReturn.total_rct_pd_cheque * salesReturn.hdr.amount_signum,
        total_rct_coupon: salesReturn.total_rct_coupon * salesReturn.hdr.amount_signum,
        total_rct_bank_transfer: salesReturn.total_rct_bank_transfer * salesReturn.hdr.amount_signum,
        total_rct_e_wallet: salesReturn.total_rct_e_wallet * salesReturn.hdr.amount_signum,
        total_rct_other: salesReturn.total_rct_other * salesReturn.hdr.amount_signum,
        hdr: {
          ...salesReturn.hdr,
          amount_txn: salesReturn.hdr.amount_txn * salesReturn.hdr.amount_signum,
          amount_discount: salesReturn.hdr.amount_discount * salesReturn.hdr.amount_signum,
          amount_tax_gst: salesReturn.hdr.amount_tax_gst * salesReturn.hdr.amount_signum,
          total_trade_in_amount_txn: salesReturn.hdr.total_trade_in_amount_txn * salesReturn.hdr.amount_signum,
        },
        section: 'Sales Return'
      }));

      const rctVoucherOpenWithSection = resolved.data[0].receiptOpen.map(receiptOpen => ({
        ...receiptOpen,
        section: 'Receipt Voucher'
      }));

      const rctVoucherWithSection = resolved.data[0].receipt.map(receipt => ({
        ...receipt,
        section: 'Collection For Other Sales'
      }));

      reportList = [...salesWithSection, ...salesReturnWithSection, ...rctVoucherOpenWithSection, ...rctVoucherWithSection];

      // Process reportList as before
      reportList = reportList.map(b => {
        const contraDocs = b.contra_doc?.map(doc =>
          DocumentShortCodesClass.serverDocTypeToShortCodeMapper(doc.server_doc_type) + doc.server_doc_1
        ).join(', ') || '';

        const linkedDocuments = b.hdr.linked_documents?.map(doc =>
          DocumentShortCodesClass.serverDocTypeToShortCodeMapper(doc.server_doc_type) + doc.server_doc_1
        ).join(', ') || '';

        const baseObject = {
          ...b,
          docShortCode: b.hdr?.['server_doc_type']
            ? DocumentShortCodesClass.serverDocTypeToShortCodeMapper(b.hdr['server_doc_type'])
            : "",
          contraDocShortCodes: contraDocs,
          linkedDocuments: linkedDocuments
        };

        // Conditionally add 'term' field if contraDocShortCodes is empty
        if (!contraDocs && b.hdr.server_doc_type!='INTERNAL_SALES_CASHBILL') {
          baseObject.term = b.hdr?.amount_txn || '';
        }

        return baseObject;
      });

      console.log('reportList', reportList);

      this.viewColFacade.loadSuccess(resolved);
      this.totalRecords = resolved.data.length;
      this.rowData = [...reportList];
      this.gridApi.setRowData(this.rowData);
      this.viewColFacade.selectRowDataByDS(this.rowData);
      this.viewColFacade.selectTotalRecordsByDS(this.totalRecords);
    }, err => {
      console.error(err);
      let msg = err.status===403 ? ErrorMessages.CRUD_403_ERRORS.READ : err.message;
      this.toastr.error(
        msg,
        "AG Gird Error",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      this.viewColFacade.loadFailed(err);
    });
  };


  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onSearch(e: SearchQueryModel) {
    console.log("search", e);
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3)  {
        this.toastr.error(
          'Search keyword must more than 2 characters.',
          'Keyword',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      }

      const inputModel = {} as SalesReportByItemCodeInputModel;
      inputModel.keyword = e.keyword;
      inputModel.date_from = '2022-01-01T00:00:00.000Z';
      inputModel.date_to = '2099-12-31T00:00:00.000Z';
      inputModel.server_doc_type = ['INTERNAL_SALES_CASHBILL','INTERNAL_SALES_INVOICE','INTERNAL_SALES_ORDER',
        'INTERNAL_PAYMENT_VOUCHER','INTERNAL_RECEIPT_VOUCHER'
      ];
      if (e.queryString) {
        inputModel.branch_guids = UtilitiesModule.checkNull(e.queryString['branch'],[]);
        inputModel.salesman_guids = UtilitiesModule.checkNull(e.queryString['salesman'],[]);
        inputModel.date_from = UtilitiesModule.checkNull(e.queryString['date']['from'],'2022-01-01T00:00:00.000Z');
        inputModel.date_to = UtilitiesModule.checkNull(e.queryString['date']['to'],'2099-12-31T00:00:00.000Z');
      }
      this.createData(inputModel);

      const date_from = UtilitiesModule.dateFormatter(inputModel.date_from);
      const date_to = UtilitiesModule.dateFormatter(inputModel.date_to);

      // Set branch names asynchronously
      this.setBranchSubTitle(inputModel.branch_guids, date_from, date_to);
    }
    else {
      //this.createData();
    }
  }

  clear() {
    this.gridApi.setRowData(null);
    this.totalRecords = 0;
    this.rowData = [];
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  setDataRowCache() {
    console.log('set data row cache');
    this.gridApi.setRowData(this.rowData);
  }

  getRowStyle = params => {
    if (params.node.footer) {
      return { fontWeight: 'bold', background: '#e6f7ff' };
    }
    if (params.node.group) {
      return { fontWeight: 'bold' };
    }
     if(params.data && (params.data.branch_name==="Sales and Collection" || params.data.branch_name==="Sales Return")){
      return { fontWeight: '', background: 'darkgrey', 'text-align': 'left' };
    }
  }

  getSalesCost(model: SalesReportByItemCodeModel) {
    return model[this.salesCost + '_amount'];
  }

  onCellClicked(e: any) {
    if (e.colDef.field === "hdr.server_doc_1") {
      this.openPopup(e.data); // Pass the row data to the popup
    }
    else if (this.cellClickHandler) {
      this.cellClickHandler.onCellClicked(null,e.data.hdr.server_doc_1, e.data.hdr.server_doc_type);
    }
  }

  openPopup(documentData: any) {
    console.log("documentData", documentData);
    let data = typeof documentData === 'string' ? JSON.parse(documentData) : documentData;
    let dtoObject = {
      "server_doc_1" : data.hdr.server_doc_1,
      "server_doc_type" : data.hdr.server_doc_type,
    }
    this.subs.sink = this.genDocService.getByGenericCriteriaSnapshot(dtoObject, this.apiVisa).subscribe(response=>{

      console.log('document data', response.data[0]);
      this.sessionStore.dispatch(SessionActions.selectDocument({document:response.data[0]}));
      this.dialog.open(AkaunGenericDocViewDialogComponent, {
        width: '80vw',
        data: response.data[0]
      });
    })
  }

  setBranchSubTitle(guids: string[], dateFrom: string, dateTo: string) {
    const key = 'branch';

    this.sessionStore.select(SessionSelectors.selectDropDownRowData).pipe(take(1)).subscribe(resolved => {
      let rowData = resolved ? resolved[key] ? resolved[key] : [] : [];
      let branchText = "ALL BRANCHES";

      if (rowData.length > 0 && guids && guids.length > 0) {
        const branches = rowData.filter(row => guids.includes(row.guid));
        if (branches.length > 0) {
          branchText = branches.map(branch => `${branch.name} (${branch.code})`).join(', ');
        }
      }

      let subTitle = branchText;
      subTitle += "\n";
      subTitle += `${dateFrom} to ${dateTo}`;

      const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
      statusBarComponent.setSubTitle(subTitle);
    });
  }
}
