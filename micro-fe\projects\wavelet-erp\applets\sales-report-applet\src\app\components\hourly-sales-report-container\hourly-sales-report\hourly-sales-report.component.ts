// Angular Core
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';

// NgRx
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';

// Third-party Libraries
import { GridOptions } from 'ag-grid-enterprise';
import { ErrorMessages } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { Observable, Subject, from } from 'rxjs';
import { debounceTime, map, mergeMap, take, toArray } from 'rxjs/operators';
import { SubSink } from 'subsink2';

// Shared Utilities - Models
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';

// Shared Utilities - Permissions
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';

// Shared Utilities - Session
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';

// Shared Utilities - Components & Utilities
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';

// Application Imports
import { ApiService } from '../../../services/api-service';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { DailySalesReportCashflowSearchModel } from '../../../models/advanced-search-models/daily-sales-report-cashflow-search.model';
import { SalesReportByItemCodeSearchModel } from '../../../models/advanced-search-models/sales-report-by-item-code-search.model';
import {
  SalesReportByItemCodeInputModel,
  SalesReportByItemCodeModel,
} from '../../../models/sales-report-by-item-code-model';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

interface SalesData {
  [hour: string]: {
    qty_sold: number;
    total_sales_amount: number;
  };
}

interface MappedData {
  branch_code: string;
  item_code: string;
  sales_data: SalesData;
}

@Component({
  selector: "app-hourly-sales-report",
  templateUrl: "./hourly-sales-report.component.html",
  styleUrls: ["./hourly-sales-report.component.css"],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore],
})
export class HourlySalesReportComponent
  extends ViewColumnComponent
  implements OnInit, OnDestroy
{
  protected subs = new SubSink();

  compId = 'hourlySalesReport';
  compName = "Hourly Sales Report";
  protected readonly index = 0;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  protected localState: LocalState;

  readonly deactivateAdd$ = this.componentStore.select(
    (state) => state.deactivateAdd
  );
  readonly deactivateList$ = this.componentStore.select(
    (state) => state.deactivateList
  );

  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  toggleColumn$: Observable<boolean>;
  searchModel = DailySalesReportCashflowSearchModel;

  showColumns = [
    { name: 'sales_cost', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
    { name: 'landed_ma_amount', setting: 'HIDE_MA_COST', permission: 'SHOW_MA_COST' },
    { name: 'gp_landed', setting: 'HIDE_GP', permission: 'SHOW_GP' },
    { name: 'gp_percentage_landed', setting: 'HIDE_GP_PERCENTAGE', permission: 'SHOW_GP_PERCENTAGE' },
  ]

  readPermissionDefintion = {
    branch: 'API_TNT_DM_ERP_HOURLY_SALES_REPORT_READ'
  }

  rowData = [];
  totalRecords = 0;
  salesCost = "cost_ma";
  branchGuids = [];

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;
  
  gridOptions: GridOptions = {
    pagination: false,
    groupIncludeTotalFooter: true
  };

  private createHourBlockColumn(hour: string) {
    return [
      {
        headerName: `${hour}:00 - ${(parseInt(hour) + 1).toString().padStart(2, '0')}:00`,
        field: 'hour',
        children: [
          this.createColumn(hour, "Qty", (params) => params.data?.sales_data?.[hour]?.qty_sold ?? 0),
          this.createColumn(hour, "Amt", (params) => params.data?.sales_data?.[hour]?.total_sales_amount?.toFixed(2) ?? '0.00'),
        ],
        cellStyle: () => ({'text-align': 'center'}),
        hide: true,
      },
    ];
  }

  private createColumn(hour: string, field: string, cellRenderer: (params) => any) {
    const isQtyColumn = field.includes("Qty");
    return {
      field: field,
      type: isQtyColumn ? "integerColumn" : "decimalColumn",
      //cellRenderer: cellRenderer,
      valueGetter: (params) => {
        // Extract the value for aggregation
        const value = isQtyColumn
          ? params.data?.sales_data?.[hour]?.qty_sold ?? 0
          : params.data?.sales_data?.[hour]?.total_sales_amount ?? 0;
        return value;
      },
      aggFunc: "sum", // Use sum aggregation for totals
      hide: true,
      width: 90,
      colId: `${hour}-${field}`,
    };
  }


  hourBlocks = [
    '00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12',
    '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'
  ];


  columnsDefs = [
    {
      headerName: "Branch",
      field: "branch_code",
      type: "textColumn"
    },
    {
      headerName: "Item Code",
      field: "item_code",
      type: "textColumn"
    },

    ...this.hourBlocks.map((hour) => this.createHourBlockColumn(hour)).flat(),

  ];

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected apiService: ApiService,
    protected toastr: ToastrService,
    protected readonly componentStore: ComponentStore<LocalState>,
    private readonly permissionStore: Store<PermissionStates>,
    protected readonly sessionStore: Store<SessionStates>,
    private cdRef: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit() {
    // Initialize branch GUIDs based on permissions
    this.branchGuids = UtilitiesModule.getTargetsByPermission(this.userPermissionTarget$,
      this.readPermissionDefintion.branch, 'bl_fi_mst_branch'
    );

    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.viewColFacade.rowDataByHS$.subscribe(
      (rowData) => (this.rowData = rowData)
    );
    this.subs.sink = this.viewColFacade.totalRecordsByHS$.subscribe(
      (totalRecords) => (this.totalRecords = totalRecords)
    );
    this.subs.sink = this.localState$.subscribe((a) => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onNext() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateAdd: true,
      deactivateList: false,
    });
    this.viewColFacade.onNextAndReset(this.index, 1);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();

    const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
    statusBarComponent.setTitle(this.compName);
    statusBarComponent.setFilename(this.compName);
    statusBarComponent.setHideGroupColumn(true);

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
  }

  createData(inputModel?: SalesReportByItemCodeInputModel) {
    //console.log("on create data....");

    this.clear();
    this.viewColFacade.loadInit(this.gridApi);
    this.subs.sink = this.apiService
      .getHourlySalesReport(inputModel, this.apiVisa)
      .subscribe(
        (resolved) => {
          //console.log(resolved);
          this.hourBlocks = this.updateHourBlock(resolved.data);
          this.rowData = resolved.data;

          const mappedData: MappedData[] = this.mapData(resolved.data);

          // this.rowData = mappedData;
          this.totalRecords = mappedData.length;

          this.changeColumnVisibilityBasedOnHour();
          this.gridApi.setRowData(mappedData);
          this.viewColFacade.selectRowDataByHS(this.rowData);
          this.viewColFacade.selectTotalRecordsByHS(this.totalRecords);

        },
        (err) => {
          console.error(err);
          let msg = err.status===403 ? ErrorMessages.CRUD_403_ERRORS.READ : err.message;
          this.toastr.error(msg, "AG Gird Error", {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300,
          });
          this.viewColFacade.loadFailed(err);
        }
      );
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onSearch(e: SearchQueryModel) {
    //console.log("search", e);
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3) {
        this.toastr.error(
          "Search keyword must more than 2 characters.",
          "Keyword",
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300,
          }
        );
        return;
      }

      const inputModel = {} as SalesReportByItemCodeInputModel;
      // inputModel.keyword = e.keyword;
      inputModel.date_from = "2023-10-01T00:00:00.000Z";
      inputModel.date_to = "2023-10-02T00:00:00.000Z";
      inputModel.branch_guids = this.branchGuids ?? [];
      if (e.queryString) {
        inputModel.branch_guids = UtilitiesModule.checkNull(
          e.queryString["branch"],
          []
        );
        inputModel.date_from = UtilitiesModule.checkNull(
          e.queryString["date"]["from"],
          "2023-10-01T00:00:00.000Z"
        );
        inputModel.date_to = UtilitiesModule.checkNull(
          e.queryString["date"]["to"],
          "2023-10-02T00:00:00.000Z"
        );
      }

      this.createData(inputModel);

      const date_from = UtilitiesModule.dateFormatter(inputModel.date_from);
      const date_to = UtilitiesModule.dateFormatter(inputModel.date_to);

      // Set branch names asynchronously
      this.setBranchSubTitle(inputModel.branch_guids, date_from, date_to);

    } else {
      this.createData();
    }
  }

  clear() {
    this.gridApi.setRowData([]);
    this.totalRecords = 0;
    this.rowData = [];
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  setDataRowCache() {
    //console.log("set data row cache");

    this.hourBlocks = this.updateHourBlock(this.rowData);

    const mappedData: MappedData[] = this.mapData(this.rowData);
    this.changeColumnVisibilityBasedOnHour();
    this.gridApi.setRowData(mappedData);

  }


  setColumnVisible(column: any, visible: boolean) {
    var colDef = column.getColDef();
    colDef.suppressColumnsToolPanel = !visible;
    this.gridColumnApi.setColumnVisible(column, visible);
    this.gridApi.refreshToolPanel();
  }

  // This function will merge the same hour data into 1 row.
  // if the branch code and item code are same, but hour is different, it should be combined and shown in 1 row.
  private mapData(originalData: any[]): MappedData[] {

    interface SalesData {
      [hour: string]: {
        qty_sold: number;
        total_sales_amount: number;
      };
    }

    interface MappedData {
      branch_code: string;
      item_code: string;
      sales_data: SalesData;
    }

    const mappedData: MappedData[] = [];

    originalData.forEach((item) => {
      const existingEntry = mappedData.find(
        (entry) =>
          entry.branch_code === item.branch_code && entry.item_code === item.item_code
      );

      if (existingEntry) {
        // Update existing entry
        if (!existingEntry.sales_data[item.hour]) {
          existingEntry.sales_data[item.hour] = {
            qty_sold: item.qty_sold,
            total_sales_amount: item.total_sales_amount,
          };
        } else {
          existingEntry.sales_data[item.hour].qty_sold += item.qty_sold;
          existingEntry.sales_data[item.hour].total_sales_amount += item.total_sales_amount;
        }
      } else {
        // Create new entry
        const newEntry: MappedData = {
          branch_code: item.branch_code,
          item_code: item.item_code,
          sales_data: {
            [item.hour]: {
              qty_sold: item.qty_sold,
              total_sales_amount: item.total_sales_amount,
            },
          },
        };
        mappedData.push(newEntry);
      }
    });

    //console.log("updated data ", mappedData)
    return mappedData;
}

// This function will update the hour block based on the data.
// by default, hour block is 0-23 hour. But his function will update hourBlock based on data.
private updateHourBlock(originalData: any[]): string[] {

  // Extract unique hours from the original data
  this.hourBlocks = [...new Set(originalData.map(item => item.hour))];
  var uniqueHours = this.hourBlocks;

  return uniqueHours;
}

changeColumnVisibilityBasedOnHour() {
  // console.log(this.hourBlocks, "Set Visibility method -- Current hour block is = ");

  const hourColumns = [];

  for(let hour of this.hourBlocks) {
    // console.log(`${hour} hour exists`);

    const qtyColumn = this.gridColumnApi.getColumn(`${hour}-Qty`);
    const amtColumn = this.gridColumnApi.getColumn(`${hour}-Amt`);

    hourColumns.push(qtyColumn, amtColumn);
  }

  //set visibility for all columns in the array
  hourColumns.forEach(column => this.setColumnVisible(column, true));

}

  setBranchSubTitle(guids: string[], dateFrom: string, dateTo: string) {
    const key = 'branch';

    this.sessionStore.select(SessionSelectors.selectDropDownRowData).pipe(take(1)).subscribe(resolved => {
      let rowData = resolved ? resolved[key] ? resolved[key] : [] : [];
      let branchText = "ALL BRANCHES";

      if (rowData.length > 0 && guids && guids.length > 0) {
        const branches = rowData.filter(row => guids.includes(row.guid));
        if (branches.length > 0) {
          branchText = branches.map(branch => `${branch.name} (${branch.code})`).join(', ');
        }
      }

      let subTitle = branchText;
      subTitle += "\n";
      subTitle += `${dateFrom} to ${dateTo}`;

      const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
      statusBarComponent.setSubTitle(subTitle);
    });
  }

}

